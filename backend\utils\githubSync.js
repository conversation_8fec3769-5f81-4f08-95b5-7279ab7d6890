const axios = require('axios');
const { Project } = require('../models');

class GitHubSync {
  constructor() {
    this.baseURL = 'https://api.github.com';
    this.username = process.env.GITHUB_USERNAME || 'chxb07';
    this.token = process.env.GITHUB_TOKEN;
    
    // Configure axios instance
    this.api = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Portfolio-App'
      }
    });

    // Add auth token if available
    if (this.token) {
      this.api.defaults.headers.common['Authorization'] = `token ${this.token}`;
    }
  }

  async fetchUserRepos(options = {}) {
    try {
      const {
        sort = 'updated',
        direction = 'desc',
        per_page = 100,
        type = 'owner'
      } = options;

      const response = await this.api.get(`/users/${this.username}/repos`, {
        params: {
          sort,
          direction,
          per_page,
          type
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching GitHub repos:', error.message);
      throw error;
    }
  }

  async fetchRepoDetails(repoName) {
    try {
      const response = await this.api.get(`/repos/${this.username}/${repoName}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching repo details for ${repoName}:`, error.message);
      throw error;
    }
  }

  async fetchRepoLanguages(repoName) {
    try {
      const response = await this.api.get(`/repos/${this.username}/${repoName}/languages`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching languages for ${repoName}:`, error.message);
      return {};
    }
  }

  async fetchRepoReadme(repoName, branch = 'main') {
    try {
      const response = await this.api.get(`/repos/${this.username}/${repoName}/readme`, {
        headers: {
          'Accept': 'application/vnd.github.v3.raw'
        }
      });
      return response.data;
    } catch (error) {
      // Try with master branch if main fails
      if (branch === 'main') {
        return this.fetchRepoReadme(repoName, 'master');
      }
      console.log(`No README found for ${repoName}`);
      return null;
    }
  }

  filterRelevantRepos(repos) {
    return repos.filter(repo => {
      // Filter out forks, archived repos, and repos with no description
      if (repo.fork || repo.archived || !repo.description) {
        return false;
      }

      // Filter out common non-project repos
      const excludeNames = [
        'dotfiles',
        'config',
        'scripts',
        'notes',
        'learning',
        'practice',
        'test',
        'demo'
      ];

      const repoName = repo.name.toLowerCase();
      return !excludeNames.some(exclude => repoName.includes(exclude));
    });
  }

  mapRepoToProject(repo, languages = {}) {
    // Determine primary language
    const primaryLanguage = repo.language || Object.keys(languages)[0] || 'Unknown';
    
    // Map languages to technologies
    const technologies = Object.keys(languages).map(lang => ({
      name: lang,
      category: this.getLanguageCategory(lang),
      icon: lang.toLowerCase()
    }));

    // Determine project category based on language and topics
    const category = this.determineProjectCategory(repo, primaryLanguage);

    // Extract features from description and topics
    const features = this.extractFeatures(repo);

    return {
      title: this.formatTitle(repo.name),
      description: repo.description || 'No description available',
      shortDescription: this.truncateDescription(repo.description, 150),
      githubUrl: repo.html_url,
      liveUrl: repo.homepage || null,
      technologies,
      features,
      status: 'completed',
      category,
      priority: this.calculatePriority(repo),
      githubData: {
        id: repo.id,
        name: repo.name,
        fullName: repo.full_name,
        language: repo.language,
        stargazersCount: repo.stargazers_count,
        forksCount: repo.forks_count,
        watchersCount: repo.watchers_count,
        size: repo.size,
        defaultBranch: repo.default_branch,
        topics: repo.topics || [],
        lastUpdated: new Date(repo.updated_at),
        isFromGithub: true
      },
      tags: repo.topics || [],
      startDate: new Date(repo.created_at),
      endDate: new Date(repo.updated_at),
      isVisible: true,
      showInPortfolio: true,
      isFeatured: this.shouldBeFeatured(repo)
    };
  }

  getLanguageCategory(language) {
    const categories = {
      'JavaScript': 'frontend',
      'TypeScript': 'frontend',
      'React': 'frontend',
      'Vue': 'frontend',
      'Angular': 'frontend',
      'HTML': 'frontend',
      'CSS': 'frontend',
      'SCSS': 'frontend',
      'Node.js': 'backend',
      'Python': 'backend',
      'Java': 'backend',
      'C#': 'backend',
      'PHP': 'backend',
      'Go': 'backend',
      'Rust': 'backend',
      'MongoDB': 'database',
      'MySQL': 'database',
      'PostgreSQL': 'database',
      'SQLite': 'database'
    };

    return categories[language] || 'other';
  }

  determineProjectCategory(repo, primaryLanguage) {
    const name = repo.name.toLowerCase();
    const description = (repo.description || '').toLowerCase();
    const topics = repo.topics || [];

    // Check for specific keywords
    if (topics.includes('mobile') || name.includes('mobile') || description.includes('mobile')) {
      return 'mobile-app';
    }
    
    if (topics.includes('api') || name.includes('api') || description.includes('api')) {
      return 'api';
    }
    
    if (topics.includes('library') || name.includes('lib') || description.includes('library')) {
      return 'library';
    }
    
    if (topics.includes('game') || name.includes('game') || description.includes('game')) {
      return 'game';
    }
    
    if (topics.includes('tool') || name.includes('tool') || description.includes('tool')) {
      return 'tool';
    }

    // Default to web-app for frontend languages
    if (['JavaScript', 'TypeScript', 'HTML', 'CSS'].includes(primaryLanguage)) {
      return 'web-app';
    }

    return 'other';
  }

  extractFeatures(repo) {
    const features = [];
    const description = repo.description || '';
    const topics = repo.topics || [];

    // Add features based on topics
    if (topics.includes('responsive')) features.push('Responsive design');
    if (topics.includes('pwa')) features.push('Progressive Web App');
    if (topics.includes('real-time')) features.push('Real-time functionality');
    if (topics.includes('authentication')) features.push('User authentication');
    if (topics.includes('database')) features.push('Database integration');
    if (topics.includes('api')) features.push('API integration');

    // Add generic features if none found
    if (features.length === 0) {
      features.push('Modern web technologies', 'Clean code architecture');
    }

    return features;
  }

  calculatePriority(repo) {
    let priority = 5; // Base priority

    // Increase priority based on stars
    if (repo.stargazers_count > 10) priority += 2;
    if (repo.stargazers_count > 50) priority += 1;

    // Increase priority based on forks
    if (repo.forks_count > 5) priority += 1;

    // Increase priority for recent activity
    const lastUpdate = new Date(repo.updated_at);
    const monthsAgo = (Date.now() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24 * 30);
    if (monthsAgo < 6) priority += 1;

    // Increase priority for repos with homepage
    if (repo.homepage) priority += 1;

    return Math.min(priority, 10);
  }

  shouldBeFeatured(repo) {
    return repo.stargazers_count > 5 || 
           repo.forks_count > 2 || 
           repo.homepage || 
           (repo.topics && repo.topics.length > 3);
  }

  formatTitle(repoName) {
    return repoName
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  truncateDescription(description, maxLength) {
    if (!description || description.length <= maxLength) {
      return description;
    }
    return description.substring(0, maxLength).trim() + '...';
  }

  async syncProjects() {
    try {
      console.log('Starting GitHub projects sync...');

      // Fetch all repos
      const repos = await this.fetchUserRepos();
      console.log(`Found ${repos.length} repositories`);

      // Filter relevant repos
      const relevantRepos = this.filterRelevantRepos(repos);
      console.log(`Filtered to ${relevantRepos.length} relevant repositories`);

      let syncedCount = 0;
      let updatedCount = 0;

      for (const repo of relevantRepos) {
        try {
          // Check if project already exists
          const existingProject = await Project.findOne({
            'githubData.id': repo.id
          });

          // Fetch additional data
          const languages = await this.fetchRepoLanguages(repo.name);
          
          // Map repo to project format
          const projectData = this.mapRepoToProject(repo, languages);

          if (existingProject) {
            // Update existing project with new GitHub data
            Object.assign(existingProject, {
              ...projectData,
              // Preserve manual overrides
              title: existingProject.title || projectData.title,
              description: existingProject.description || projectData.description,
              isVisible: existingProject.isVisible,
              isFeatured: existingProject.isFeatured,
              priority: existingProject.priority || projectData.priority
            });

            await existingProject.save();
            updatedCount++;
            console.log(`Updated: ${repo.name}`);
          } else {
            // Create new project
            await Project.create(projectData);
            syncedCount++;
            console.log(`Created: ${repo.name}`);
          }
        } catch (error) {
          console.error(`Error processing ${repo.name}:`, error.message);
        }
      }

      console.log(`\n✅ Sync completed!`);
      console.log(`📊 Results:`);
      console.log(`   - New projects: ${syncedCount}`);
      console.log(`   - Updated projects: ${updatedCount}`);
      console.log(`   - Total processed: ${syncedCount + updatedCount}`);

      return {
        success: true,
        synced: syncedCount,
        updated: updatedCount,
        total: syncedCount + updatedCount
      };
    } catch (error) {
      console.error('GitHub sync failed:', error);
      throw error;
    }
  }
}

module.exports = GitHubSync;
