import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff,
  Star,
  ExternalLink,
  Github,
  X
} from 'lucide-react';
import AdminLayout from '../../components/admin/AdminLayout';
import { usePortfolio } from '../../contexts/PortfolioContext';
import LoadingSpinner from '../../components/LoadingSpinner';
import ProjectModal from '../../components/admin/ProjectModal';

const Projects = () => {
  const { fetchProjects, deleteProject, updateProject } = usePortfolio();
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showModal, setShowModal] = useState(false);
  const [editingProject, setEditingProject] = useState(null);
  const [filteredProjects, setFilteredProjects] = useState([]);

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'web-app', label: 'Web Apps' },
    { value: 'mobile-app', label: 'Mobile Apps' },
    { value: 'api', label: 'APIs' },
    { value: 'library', label: 'Libraries' },
    { value: 'tool', label: 'Tools' },
    { value: 'other', label: 'Other' }
  ];

  const statuses = [
    { value: 'all', label: 'All Statuses' },
    { value: 'planning', label: 'Planning' },
    { value: 'in-progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'on-hold', label: 'On Hold' },
    { value: 'archived', label: 'Archived' }
  ];

  useEffect(() => {
    loadProjects();
  }, []);

  useEffect(() => {
    filterProjects();
  }, [projects, searchTerm, selectedCategory, selectedStatus]);

  const loadProjects = async () => {
    setLoading(true);
    const result = await fetchProjects();
    if (result?.projects) {
      setProjects(result.projects);
    }
    setLoading(false);
  };

  const filterProjects = () => {
    let filtered = projects;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.technologies.some(tech => 
          tech.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(project => project.category === selectedCategory);
    }

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(project => project.status === selectedStatus);
    }

    setFilteredProjects(filtered);
  };

  const handleEdit = (project) => {
    setEditingProject(project);
    setShowModal(true);
  };

  const handleDelete = async (projectId) => {
    if (window.confirm('Are you sure you want to delete this project?')) {
      const result = await deleteProject(projectId);
      if (result.success) {
        setProjects(projects.filter(p => p._id !== projectId));
      }
    }
  };

  const handleToggleVisibility = async (project) => {
    const result = await updateProject(project._id, {
      isVisible: !project.isVisible
    });
    if (result.success) {
      setProjects(projects.map(p => 
        p._id === project._id ? { ...p, isVisible: !p.isVisible } : p
      ));
    }
  };

  const handleToggleFeatured = async (project) => {
    const result = await updateProject(project._id, {
      isFeatured: !project.isFeatured
    });
    if (result.success) {
      setProjects(projects.map(p => 
        p._id === project._id ? { ...p, isFeatured: !p.isFeatured } : p
      ));
    }
  };

  const handleModalClose = () => {
    setShowModal(false);
    setEditingProject(null);
  };

  const handleProjectSaved = (savedProject) => {
    if (editingProject) {
      setProjects(projects.map(p => 
        p._id === savedProject._id ? savedProject : p
      ));
    } else {
      setProjects([savedProject, ...projects]);
    }
    handleModalClose();
  };

  const getStatusColor = (status) => {
    const colors = {
      planning: 'bg-yellow-500/20 text-yellow-400',
      'in-progress': 'bg-blue-500/20 text-blue-400',
      completed: 'bg-green-500/20 text-green-400',
      'on-hold': 'bg-orange-500/20 text-orange-400',
      archived: 'bg-gray-500/20 text-gray-400'
    };
    return colors[status] || 'bg-gray-500/20 text-gray-400';
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  };

  return (
    <AdminLayout title="Projects">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >
        {/* Header */}
        <motion.div variants={itemVariants} className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-white">Projects</h2>
            <p className="text-gray-400">Manage your portfolio projects</p>
          </div>
          
          <motion.button
            onClick={() => setShowModal(true)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="btn-primary"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Project
          </motion.button>
        </motion.div>

        {/* Filters */}
        <motion.div variants={itemVariants} className="card">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 glass rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neon-blue/50"
              />
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="input"
            >
              {categories.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>

            {/* Status Filter */}
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="input"
            >
              {statuses.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>

            {/* Results Count */}
            <div className="flex items-center text-gray-400">
              <Filter className="w-4 h-4 mr-2" />
              <span className="text-sm">
                {filteredProjects.length} of {projects.length} projects
              </span>
            </div>
          </div>
        </motion.div>

        {/* Projects Grid */}
        {loading ? (
          <div className="flex justify-center py-20">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <motion.div
            variants={containerVariants}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            <AnimatePresence>
              {filteredProjects.map((project) => (
                <motion.div
                  key={project._id}
                  variants={itemVariants}
                  layout
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="card-hover group relative"
                >
                  {/* Project Image */}
                  <div className="relative h-40 mb-4 rounded-lg overflow-hidden bg-gradient-to-br from-neon-blue/20 to-neon-purple/20">
                    {project.images && project.images.length > 0 ? (
                      <img
                        src={project.images[0].url}
                        alt={project.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Github size={32} className="text-neon-blue/50" />
                      </div>
                    )}
                    
                    {/* Status Badge */}
                    <div className={`absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                      {project.status.replace('-', ' ')}
                    </div>

                    {/* Featured Badge */}
                    {project.isFeatured && (
                      <div className="absolute top-2 right-2 p-1 bg-yellow-500/20 rounded-full">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      </div>
                    )}
                  </div>

                  {/* Project Info */}
                  <div className="space-y-3">
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1 group-hover:text-neon-blue transition-colors">
                        {project.title}
                      </h3>
                      <p className="text-gray-400 text-sm line-clamp-2">
                        {project.shortDescription || project.description}
                      </p>
                    </div>

                    {/* Technologies */}
                    {project.technologies && project.technologies.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {project.technologies.slice(0, 3).map((tech, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 text-xs bg-gray-800 text-gray-300 rounded"
                          >
                            {tech.name}
                          </span>
                        ))}
                        {project.technologies.length > 3 && (
                          <span className="px-2 py-1 text-xs bg-gray-800 text-gray-400 rounded">
                            +{project.technologies.length - 3}
                          </span>
                        )}
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-3 border-t border-white/10">
                      <div className="flex space-x-2">
                        {project.liveUrl && (
                          <a
                            href={project.liveUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-2 glass rounded-lg hover:bg-neon-blue/20 transition-colors"
                            title="View Live"
                          >
                            <ExternalLink className="w-4 h-4 text-gray-400 hover:text-neon-blue" />
                          </a>
                        )}
                        
                        <a
                          href={project.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 glass rounded-lg hover:bg-neon-blue/20 transition-colors"
                          title="View Code"
                        >
                          <Github className="w-4 h-4 text-gray-400 hover:text-neon-blue" />
                        </a>
                      </div>

                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleToggleVisibility(project)}
                          className={`p-2 glass rounded-lg transition-colors ${
                            project.isVisible 
                              ? 'hover:bg-green-500/20 text-green-400' 
                              : 'hover:bg-red-500/20 text-red-400'
                          }`}
                          title={project.isVisible ? 'Hide' : 'Show'}
                        >
                          {project.isVisible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                        </button>

                        <button
                          onClick={() => handleToggleFeatured(project)}
                          className={`p-2 glass rounded-lg transition-colors ${
                            project.isFeatured 
                              ? 'hover:bg-yellow-500/20 text-yellow-400' 
                              : 'hover:bg-gray-500/20 text-gray-400'
                          }`}
                          title={project.isFeatured ? 'Unfeature' : 'Feature'}
                        >
                          <Star className={`w-4 h-4 ${project.isFeatured ? 'fill-current' : ''}`} />
                        </button>

                        <button
                          onClick={() => handleEdit(project)}
                          className="p-2 glass rounded-lg hover:bg-neon-blue/20 transition-colors"
                          title="Edit"
                        >
                          <Edit className="w-4 h-4 text-gray-400 hover:text-neon-blue" />
                        </button>

                        <button
                          onClick={() => handleDelete(project._id)}
                          className="p-2 glass rounded-lg hover:bg-red-500/20 transition-colors"
                          title="Delete"
                        >
                          <Trash2 className="w-4 h-4 text-gray-400 hover:text-red-400" />
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>
        )}

        {/* Empty State */}
        {!loading && filteredProjects.length === 0 && (
          <motion.div
            variants={itemVariants}
            className="text-center py-20"
          >
            <Github size={64} className="text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-400 mb-2">
              No projects found
            </h3>
            <p className="text-gray-500 mb-6">
              {projects.length === 0 
                ? "Get started by adding your first project"
                : "Try adjusting your filters or search term"
              }
            </p>
            {projects.length === 0 && (
              <button
                onClick={() => setShowModal(true)}
                className="btn-primary"
              >
                <Plus className="w-5 h-5 mr-2" />
                Add Your First Project
              </button>
            )}
          </motion.div>
        )}
      </motion.div>

      {/* Project Modal */}
      <ProjectModal
        isOpen={showModal}
        onClose={handleModalClose}
        project={editingProject}
        onSave={handleProjectSaved}
      />
    </AdminLayout>
  );
};

export default Projects;
