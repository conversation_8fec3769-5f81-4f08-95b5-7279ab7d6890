const express = require('express');
const rateLimit = require('express-rate-limit');
const {
  createMessage,
  getMessages,
  getMessage,
  updateMessageStatus,
  toggleStar,
  replyToMessage,
  markAsSpam,
  deleteMessage,
  getMessageStats
} = require('../controllers/messageController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { validateContactMessage } = require('../middleware/validation');

const router = express.Router();

// Rate limiting for contact form
const contactLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 messages per hour
  message: {
    success: false,
    message: 'Too many messages sent, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Public routes
// @route   POST /api/messages
// @desc    Create new contact message
// @access  Public
router.post('/', contactLimiter, validateContactMessage, createMessage);

// Admin routes
// @route   GET /api/messages
// @desc    Get all messages (admin)
// @access  Private/Admin
router.get('/', authenticateToken, requireAdmin, getMessages);

// @route   GET /api/messages/stats
// @desc    Get message statistics
// @access  Private/Admin
router.get('/stats', authenticateToken, requireAdmin, getMessageStats);

// @route   GET /api/messages/:id
// @desc    Get single message
// @access  Private/Admin
router.get('/:id', authenticateToken, requireAdmin, getMessage);

// @route   PATCH /api/messages/:id/status
// @desc    Update message status
// @access  Private/Admin
router.patch('/:id/status', authenticateToken, requireAdmin, updateMessageStatus);

// @route   PATCH /api/messages/:id/star
// @desc    Toggle message star
// @access  Private/Admin
router.patch('/:id/star', authenticateToken, requireAdmin, toggleStar);

// @route   POST /api/messages/:id/reply
// @desc    Reply to message
// @access  Private/Admin
router.post('/:id/reply', authenticateToken, requireAdmin, replyToMessage);

// @route   PATCH /api/messages/:id/spam
// @desc    Mark message as spam
// @access  Private/Admin
router.patch('/:id/spam', authenticateToken, requireAdmin, markAsSpam);

// @route   DELETE /api/messages/:id
// @desc    Delete message
// @access  Private/Admin
router.delete('/:id', authenticateToken, requireAdmin, deleteMessage);

module.exports = router;
