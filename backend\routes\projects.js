const express = require('express');
const rateLimit = require('express-rate-limit');
const {
  getProjects,
  getFeaturedProjects,
  getProject,
  getProjectsAdmin,
  createProject,
  updateProject,
  deleteProject,
  toggleVisibility,
  toggleFeatured,
  trackClick
} = require('../controllers/projectController');
const { authenticateToken, requireAdmin, optionalAuth } = require('../middleware/auth');
const { validateProject } = require('../middleware/validation');

const router = express.Router();

// Rate limiting for project clicks
const clickLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // limit each IP to 10 clicks per minute
  message: {
    success: false,
    message: 'Too many click requests, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Public routes
// @route   GET /api/projects
// @desc    Get all projects (public)
// @access  Public
router.get('/', optionalAuth, getProjects);

// @route   GET /api/projects/featured
// @desc    Get featured projects
// @access  Public
router.get('/featured', getFeaturedProjects);

// @route   GET /api/projects/:id
// @desc    Get single project
// @access  Public
router.get('/:id', optionalAuth, getProject);

// @route   POST /api/projects/:id/click
// @desc    Track project click
// @access  Public
router.post('/:id/click', clickLimiter, trackClick);

// Admin routes
// @route   GET /api/projects/admin/all
// @desc    Get all projects (admin)
// @access  Private/Admin
router.get('/admin/all', authenticateToken, requireAdmin, getProjectsAdmin);

// @route   POST /api/projects
// @desc    Create new project
// @access  Private/Admin
router.post('/', authenticateToken, requireAdmin, validateProject, createProject);

// @route   PUT /api/projects/:id
// @desc    Update project
// @access  Private/Admin
router.put('/:id', authenticateToken, requireAdmin, validateProject, updateProject);

// @route   DELETE /api/projects/:id
// @desc    Delete project
// @access  Private/Admin
router.delete('/:id', authenticateToken, requireAdmin, deleteProject);

// @route   PATCH /api/projects/:id/visibility
// @desc    Toggle project visibility
// @access  Private/Admin
router.patch('/:id/visibility', authenticateToken, requireAdmin, toggleVisibility);

// @route   PATCH /api/projects/:id/featured
// @desc    Toggle featured status
// @access  Private/Admin
router.patch('/:id/featured', authenticateToken, requireAdmin, toggleFeatured);

module.exports = router;
