const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  // Sender Information
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters'],
    minlength: [2, 'Name must be at least 2 characters long']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email address']
  },
  phone: {
    type: String,
    trim: true,
    default: null,
    maxlength: [20, 'Phone number cannot exceed 20 characters']
  },
  company: {
    type: String,
    trim: true,
    default: null,
    maxlength: [100, 'Company name cannot exceed 100 characters']
  },
  
  // Message Content
  subject: {
    type: String,
    required: [true, 'Subject is required'],
    trim: true,
    maxlength: [200, 'Subject cannot exceed 200 characters'],
    minlength: [5, 'Subject must be at least 5 characters long']
  },
  message: {
    type: String,
    required: [true, 'Message is required'],
    trim: true,
    maxlength: [2000, 'Message cannot exceed 2000 characters'],
    minlength: [10, 'Message must be at least 10 characters long']
  },
  
  // Message Type and Priority
  type: {
    type: String,
    enum: ['general', 'project-inquiry', 'job-opportunity', 'collaboration', 'feedback', 'other'],
    default: 'general'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  
  // Message Status
  status: {
    type: String,
    enum: ['unread', 'read', 'replied', 'archived', 'spam'],
    default: 'unread'
  },
  isStarred: {
    type: Boolean,
    default: false
  },
  
  // Response Information
  response: {
    message: {
      type: String,
      default: null,
      maxlength: [2000, 'Response cannot exceed 2000 characters']
    },
    sentAt: {
      type: Date,
      default: null
    },
    sentBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      default: null
    }
  },
  
  // Technical Information
  ipAddress: {
    type: String,
    default: null
  },
  userAgent: {
    type: String,
    default: null
  },
  referrer: {
    type: String,
    default: null
  },
  
  // Spam Detection
  spamScore: {
    type: Number,
    min: [0, 'Spam score cannot be negative'],
    max: [100, 'Spam score cannot exceed 100'],
    default: 0
  },
  isSpam: {
    type: Boolean,
    default: false
  },
  
  // Timestamps
  readAt: {
    type: Date,
    default: null
  },
  repliedAt: {
    type: Date,
    default: null
  },
  archivedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

// Indexes for better query performance
messageSchema.index({ status: 1, createdAt: -1 });
messageSchema.index({ email: 1, createdAt: -1 });
messageSchema.index({ type: 1, status: 1 });
messageSchema.index({ isStarred: 1, createdAt: -1 });
messageSchema.index({ isSpam: 1, status: 1 });

// Virtual for message age
messageSchema.virtual('age').get(function() {
  const now = new Date();
  const diffTime = Math.abs(now - this.createdAt);
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
  const diffMinutes = Math.floor(diffTime / (1000 * 60));
  
  if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  if (diffMinutes > 0) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  return 'Just now';
});

// Virtual for full name with company
messageSchema.virtual('fullIdentity').get(function() {
  if (this.company) {
    return `${this.name} (${this.company})`;
  }
  return this.name;
});

// Static method to get unread count
messageSchema.statics.getUnreadCount = function() {
  return this.countDocuments({ status: 'unread', isSpam: false });
};

// Static method to get messages by status
messageSchema.statics.getByStatus = function(status, limit = null) {
  const query = this.find({ status, isSpam: false })
    .sort({ createdAt: -1 });
  
  return limit ? query.limit(limit) : query;
};

// Static method to get recent messages
messageSchema.statics.getRecent = function(limit = 10) {
  return this.find({ isSpam: false })
    .sort({ createdAt: -1 })
    .limit(limit);
};

// Static method to search messages
messageSchema.statics.search = function(searchTerm, options = {}) {
  const {
    status = null,
    type = null,
    limit = 20,
    skip = 0
  } = options;
  
  const searchRegex = new RegExp(searchTerm, 'i');
  const query = {
    isSpam: false,
    $or: [
      { name: searchRegex },
      { email: searchRegex },
      { subject: searchRegex },
      { message: searchRegex },
      { company: searchRegex }
    ]
  };
  
  if (status) query.status = status;
  if (type) query.type = type;
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Instance method to mark as read
messageSchema.methods.markAsRead = function() {
  if (this.status === 'unread') {
    this.status = 'read';
    this.readAt = new Date();
    return this.save();
  }
  return Promise.resolve(this);
};

// Instance method to mark as replied
messageSchema.methods.markAsReplied = function(responseMessage, userId) {
  this.status = 'replied';
  this.repliedAt = new Date();
  this.response.message = responseMessage;
  this.response.sentAt = new Date();
  this.response.sentBy = userId;
  return this.save();
};

// Instance method to toggle star
messageSchema.methods.toggleStar = function() {
  this.isStarred = !this.isStarred;
  return this.save();
};

// Instance method to archive
messageSchema.methods.archive = function() {
  this.status = 'archived';
  this.archivedAt = new Date();
  return this.save();
};

// Instance method to mark as spam
messageSchema.methods.markAsSpam = function() {
  this.isSpam = true;
  this.status = 'spam';
  this.spamScore = 100;
  return this.save();
};

// Pre-save middleware for spam detection (basic)
messageSchema.pre('save', function(next) {
  if (this.isNew) {
    // Basic spam detection
    const spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations', 'click here', 'free money'];
    const messageText = (this.message + ' ' + this.subject).toLowerCase();
    
    let spamScore = 0;
    spamKeywords.forEach(keyword => {
      if (messageText.includes(keyword)) {
        spamScore += 20;
      }
    });
    
    // Check for excessive caps
    const capsRatio = (this.message.match(/[A-Z]/g) || []).length / this.message.length;
    if (capsRatio > 0.5) spamScore += 15;
    
    // Check for excessive exclamation marks
    const exclamationCount = (this.message.match(/!/g) || []).length;
    if (exclamationCount > 3) spamScore += 10;
    
    this.spamScore = Math.min(spamScore, 100);
    this.isSpam = spamScore >= 60;
    
    if (this.isSpam) {
      this.status = 'spam';
    }
  }
  next();
});

module.exports = mongoose.model('Message', messageSchema);
