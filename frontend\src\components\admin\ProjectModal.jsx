import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Trash2, Save } from 'lucide-react';
import { usePortfolio } from '../../contexts/PortfolioContext';
import LoadingSpinner from '../LoadingSpinner';

const ProjectModal = ({ isOpen, onClose, project, onSave }) => {
  const { createProject, updateProject } = usePortfolio();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    shortDescription: '',
    githubUrl: '',
    liveUrl: '',
    status: 'completed',
    category: 'web-app',
    priority: 5,
    technologies: [],
    features: [],
    tags: [],
    images: [],
    isVisible: true,
    isFeatured: false,
    showInPortfolio: true
  });

  const [newTechnology, setNewTechnology] = useState({ name: '', category: 'frontend' });
  const [newFeature, setNewFeature] = useState('');
  const [newTag, setNewTag] = useState('');

  const categories = [
    { value: 'web-app', label: 'Web Application' },
    { value: 'mobile-app', label: 'Mobile Application' },
    { value: 'api', label: 'API' },
    { value: 'library', label: 'Library' },
    { value: 'tool', label: 'Tool' },
    { value: 'game', label: 'Game' },
    { value: 'other', label: 'Other' }
  ];

  const statuses = [
    { value: 'planning', label: 'Planning' },
    { value: 'in-progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'on-hold', label: 'On Hold' },
    { value: 'archived', label: 'Archived' }
  ];

  const techCategories = [
    { value: 'frontend', label: 'Frontend' },
    { value: 'backend', label: 'Backend' },
    { value: 'database', label: 'Database' },
    { value: 'tool', label: 'Tool' },
    { value: 'framework', label: 'Framework' },
    { value: 'library', label: 'Library' },
    { value: 'other', label: 'Other' }
  ];

  useEffect(() => {
    if (project) {
      setFormData({
        title: project.title || '',
        description: project.description || '',
        shortDescription: project.shortDescription || '',
        githubUrl: project.githubUrl || '',
        liveUrl: project.liveUrl || '',
        status: project.status || 'completed',
        category: project.category || 'web-app',
        priority: project.priority || 5,
        technologies: project.technologies || [],
        features: project.features || [],
        tags: project.tags || [],
        images: project.images || [],
        isVisible: project.isVisible !== undefined ? project.isVisible : true,
        isFeatured: project.isFeatured || false,
        showInPortfolio: project.showInPortfolio !== undefined ? project.showInPortfolio : true
      });
    } else {
      // Reset form for new project
      setFormData({
        title: '',
        description: '',
        shortDescription: '',
        githubUrl: '',
        liveUrl: '',
        status: 'completed',
        category: 'web-app',
        priority: 5,
        technologies: [],
        features: [],
        tags: [],
        images: [],
        isVisible: true,
        isFeatured: false,
        showInPortfolio: true
      });
    }
  }, [project, isOpen]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAddTechnology = () => {
    if (newTechnology.name.trim()) {
      setFormData(prev => ({
        ...prev,
        technologies: [...prev.technologies, { ...newTechnology }]
      }));
      setNewTechnology({ name: '', category: 'frontend' });
    }
  };

  const handleRemoveTechnology = (index) => {
    setFormData(prev => ({
      ...prev,
      technologies: prev.technologies.filter((_, i) => i !== index)
    }));
  };

  const handleAddFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }));
      setNewFeature('');
    }
  };

  const handleRemoveFeature = (index) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }));
  };

  const handleAddTag = () => {
    if (newTag.trim()) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim().toLowerCase()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (index) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      let result;
      if (project) {
        result = await updateProject(project._id, formData);
      } else {
        result = await createProject(formData);
      }

      if (result.success) {
        onSave(result.data);
      }
    } catch (error) {
      console.error('Failed to save project:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          onClick={(e) => e.stopPropagation()}
          className="w-full max-w-4xl max-h-[90vh] overflow-y-auto glass-strong rounded-2xl border border-white/20"
        >
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between border-b border-white/10 pb-4">
              <h2 className="text-2xl font-bold text-white">
                {project ? 'Edit Project' : 'Add New Project'}
              </h2>
              <button
                type="button"
                onClick={onClose}
                className="p-2 glass rounded-lg hover:bg-white/10 transition-colors"
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white">Basic Information</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Project Title *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    required
                    className="input"
                    placeholder="My Awesome Project"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Short Description
                  </label>
                  <input
                    type="text"
                    name="shortDescription"
                    value={formData.shortDescription}
                    onChange={handleChange}
                    className="input"
                    placeholder="Brief description for cards"
                    maxLength={200}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Full Description *
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    required
                    rows={4}
                    className="textarea"
                    placeholder="Detailed project description..."
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Category
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      className="input"
                    >
                      {categories.map(cat => (
                        <option key={cat.value} value={cat.value}>
                          {cat.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Status
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleChange}
                      className="input"
                    >
                      {statuses.map(status => (
                        <option key={status.value} value={status.value}>
                          {status.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Priority (1-10)
                  </label>
                  <input
                    type="number"
                    name="priority"
                    value={formData.priority}
                    onChange={handleChange}
                    min="1"
                    max="10"
                    className="input"
                  />
                </div>
              </div>

              {/* URLs and Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white">URLs & Settings</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    GitHub URL *
                  </label>
                  <input
                    type="url"
                    name="githubUrl"
                    value={formData.githubUrl}
                    onChange={handleChange}
                    required
                    className="input"
                    placeholder="https://github.com/username/repo"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Live Demo URL
                  </label>
                  <input
                    type="url"
                    name="liveUrl"
                    value={formData.liveUrl}
                    onChange={handleChange}
                    className="input"
                    placeholder="https://myproject.com"
                  />
                </div>

                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="isVisible"
                      checked={formData.isVisible}
                      onChange={handleChange}
                      className="w-4 h-4 text-neon-blue bg-transparent border-gray-600 rounded focus:ring-neon-blue/50"
                    />
                    <span className="ml-2 text-sm text-gray-300">Visible in portfolio</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="isFeatured"
                      checked={formData.isFeatured}
                      onChange={handleChange}
                      className="w-4 h-4 text-neon-blue bg-transparent border-gray-600 rounded focus:ring-neon-blue/50"
                    />
                    <span className="ml-2 text-sm text-gray-300">Featured project</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="showInPortfolio"
                      checked={formData.showInPortfolio}
                      onChange={handleChange}
                      className="w-4 h-4 text-neon-blue bg-transparent border-gray-600 rounded focus:ring-neon-blue/50"
                    />
                    <span className="ml-2 text-sm text-gray-300">Show in public portfolio</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Technologies */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Technologies</h3>
              
              <div className="flex gap-2 mb-4">
                <input
                  type="text"
                  value={newTechnology.name}
                  onChange={(e) => setNewTechnology(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Technology name"
                  className="flex-1 input"
                />
                <select
                  value={newTechnology.category}
                  onChange={(e) => setNewTechnology(prev => ({ ...prev, category: e.target.value }))}
                  className="input w-32"
                >
                  {techCategories.map(cat => (
                    <option key={cat.value} value={cat.value}>
                      {cat.label}
                    </option>
                  ))}
                </select>
                <button
                  type="button"
                  onClick={handleAddTechnology}
                  className="btn-secondary px-4"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>

              <div className="flex flex-wrap gap-2">
                {formData.technologies.map((tech, index) => (
                  <span
                    key={index}
                    className="flex items-center px-3 py-1 bg-neon-blue/20 text-neon-blue rounded-full text-sm"
                  >
                    {tech.name}
                    <button
                      type="button"
                      onClick={() => handleRemoveTechnology(index)}
                      className="ml-2 text-neon-blue/70 hover:text-red-400"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>

            {/* Features */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Features</h3>
              
              <div className="flex gap-2 mb-4">
                <input
                  type="text"
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder="Project feature"
                  className="flex-1 input"
                />
                <button
                  type="button"
                  onClick={handleAddFeature}
                  className="btn-secondary px-4"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>

              <div className="space-y-2">
                {formData.features.map((feature, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 glass rounded-lg"
                  >
                    <span className="text-gray-300">{feature}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveFeature(index)}
                      className="text-gray-400 hover:text-red-400"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Tags */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Tags</h3>
              
              <div className="flex gap-2 mb-4">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add tag"
                  className="flex-1 input"
                />
                <button
                  type="button"
                  onClick={handleAddTag}
                  className="btn-secondary px-4"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>

              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="flex items-center px-3 py-1 bg-gray-700 text-gray-300 rounded-full text-sm"
                  >
                    #{tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(index)}
                      className="ml-2 text-gray-400 hover:text-red-400"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-white/10">
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary"
              >
                Cancel
              </button>
              
              <button
                type="submit"
                disabled={loading}
                className="btn-primary disabled:opacity-50"
              >
                {loading ? (
                  <LoadingSpinner size="sm" className="mr-2" />
                ) : (
                  <Save className="w-5 h-5 mr-2" />
                )}
                {loading ? 'Saving...' : (project ? 'Update Project' : 'Create Project')}
              </button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ProjectModal;
