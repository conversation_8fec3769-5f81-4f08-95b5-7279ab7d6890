const mongoose = require('mongoose');

const portfolioSchema = new mongoose.Schema({
  // About Me Section
  about: {
    title: {
      type: String,
      default: 'About Me',
      maxlength: [100, 'Title cannot exceed 100 characters']
    },
    name: {
      type: String,
      required: [true, 'Name is required'],
      trim: true,
      maxlength: [100, 'Name cannot exceed 100 characters']
    },
    tagline: {
      type: String,
      required: [true, 'Tagline is required'],
      trim: true,
      maxlength: [200, 'Tagline cannot exceed 200 characters']
    },
    description: {
      type: String,
      required: [true, 'Description is required'],
      trim: true,
      maxlength: [2000, 'Description cannot exceed 2000 characters']
    },
    profileImage: {
      type: String,
      default: null
    },
    resumeUrl: {
      type: String,
      default: null
    },
    isVisible: {
      type: Boolean,
      default: true
    }
  },

  // Skills Section
  skills: {
    title: {
      type: String,
      default: 'Skills & Technologies',
      maxlength: [100, 'Title cannot exceed 100 characters']
    },
    categories: [{
      name: {
        type: String,
        required: true,
        trim: true,
        maxlength: [50, 'Category name cannot exceed 50 characters']
      },
      skills: [{
        name: {
          type: String,
          required: true,
          trim: true,
          maxlength: [50, 'Skill name cannot exceed 50 characters']
        },
        level: {
          type: Number,
          min: [1, 'Skill level must be at least 1'],
          max: [100, 'Skill level cannot exceed 100'],
          default: 50
        },
        icon: {
          type: String,
          default: null
        }
      }]
    }],
    isVisible: {
      type: Boolean,
      default: true
    }
  },

  // Contact Information
  contact: {
    title: {
      type: String,
      default: 'Get In Touch',
      maxlength: [100, 'Title cannot exceed 100 characters']
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    phone: {
      type: String,
      default: null,
      maxlength: [20, 'Phone number cannot exceed 20 characters']
    },
    location: {
      type: String,
      default: null,
      maxlength: [100, 'Location cannot exceed 100 characters']
    },
    socialLinks: {
      github: {
        type: String,
        default: 'https://github.com/chxb07'
      },
      linkedin: {
        type: String,
        default: 'https://linkedin.com/in/cherif-bourechache-3099432a8'
      },
      twitter: {
        type: String,
        default: null
      },
      website: {
        type: String,
        default: null
      }
    },
    isVisible: {
      type: Boolean,
      default: true
    }
  },

  // Section Visibility Controls
  sectionVisibility: {
    hero: {
      type: Boolean,
      default: true
    },
    about: {
      type: Boolean,
      default: true
    },
    skills: {
      type: Boolean,
      default: true
    },
    projects: {
      type: Boolean,
      default: true
    },
    contact: {
      type: Boolean,
      default: true
    }
  },

  // SEO and Meta Information
  seo: {
    title: {
      type: String,
      default: 'Cherif Bourechache - Computer Science Student & Frontend Developer',
      maxlength: [60, 'SEO title cannot exceed 60 characters']
    },
    description: {
      type: String,
      default: 'Portfolio of Cherif Bourechache, a passionate computer science student and frontend developer specializing in modern web technologies.',
      maxlength: [160, 'SEO description cannot exceed 160 characters']
    },
    keywords: {
      type: [String],
      default: ['frontend developer', 'computer science', 'web development', 'react', 'javascript']
    }
  },

  // Theme and Customization
  theme: {
    primaryColor: {
      type: String,
      default: '#0ea5e9'
    },
    accentColor: {
      type: String,
      default: '#00f5ff'
    },
    darkMode: {
      type: Boolean,
      default: true
    }
  }
}, {
  timestamps: true
});

// Ensure only one portfolio document exists
portfolioSchema.index({ _id: 1 }, { unique: true });

// Static method to get or create portfolio
portfolioSchema.statics.getPortfolio = async function() {
  let portfolio = await this.findOne();
  
  if (!portfolio) {
    // Create default portfolio if none exists
    portfolio = await this.create({
      about: {
        name: 'Cherif Bourechache',
        tagline: 'Computer Science Student & Frontend Developer',
        description: 'Passionate about creating modern, responsive web applications with cutting-edge technologies. Currently pursuing Computer Science while building innovative projects and expanding my expertise in frontend development.'
      },
      contact: {
        email: '<EMAIL>'
      }
    });
  }
  
  return portfolio;
};

// Instance method to update section visibility
portfolioSchema.methods.updateSectionVisibility = function(section, isVisible) {
  if (this.sectionVisibility.hasOwnProperty(section)) {
    this.sectionVisibility[section] = isVisible;
    return this.save();
  }
  throw new Error('Invalid section name');
};

module.exports = mongoose.model('Portfolio', portfolioSchema);
