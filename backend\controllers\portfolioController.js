const { Portfolio } = require('../models');

// @desc    Get portfolio data
// @route   GET /api/portfolio
// @access  Public
const getPortfolio = async (req, res) => {
  try {
    const portfolio = await Portfolio.getPortfolio();
    
    // Filter out invisible sections for public access
    const publicPortfolio = {
      ...portfolio.toObject(),
      about: portfolio.sectionVisibility.about ? portfolio.about : null,
      skills: portfolio.sectionVisibility.skills ? portfolio.skills : null,
      contact: portfolio.sectionVisibility.contact ? portfolio.contact : null,
      sectionVisibility: portfolio.sectionVisibility
    };

    res.json({
      success: true,
      data: publicPortfolio
    });
  } catch (error) {
    console.error('Get portfolio error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching portfolio'
    });
  }
};

// @desc    Get full portfolio data (admin)
// @route   GET /api/portfolio/admin
// @access  Private/Admin
const getPortfolioAdmin = async (req, res) => {
  try {
    const portfolio = await Portfolio.getPortfolio();
    
    res.json({
      success: true,
      data: portfolio
    });
  } catch (error) {
    console.error('Get portfolio admin error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching portfolio'
    });
  }
};

// @desc    Update about section
// @route   PUT /api/portfolio/about
// @access  Private/Admin
const updateAbout = async (req, res) => {
  try {
    const portfolio = await Portfolio.getPortfolio();
    
    const { name, tagline, description, profileImage, resumeUrl, title } = req.body;
    
    // Update about section
    if (title) portfolio.about.title = title;
    if (name) portfolio.about.name = name;
    if (tagline) portfolio.about.tagline = tagline;
    if (description) portfolio.about.description = description;
    if (profileImage !== undefined) portfolio.about.profileImage = profileImage;
    if (resumeUrl !== undefined) portfolio.about.resumeUrl = resumeUrl;
    
    await portfolio.save();
    
    res.json({
      success: true,
      message: 'About section updated successfully',
      data: portfolio.about
    });
  } catch (error) {
    console.error('Update about error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating about section'
    });
  }
};

// @desc    Update skills section
// @route   PUT /api/portfolio/skills
// @access  Private/Admin
const updateSkills = async (req, res) => {
  try {
    const portfolio = await Portfolio.getPortfolio();
    
    const { title, categories } = req.body;
    
    // Update skills section
    if (title) portfolio.skills.title = title;
    if (categories) portfolio.skills.categories = categories;
    
    await portfolio.save();
    
    res.json({
      success: true,
      message: 'Skills section updated successfully',
      data: portfolio.skills
    });
  } catch (error) {
    console.error('Update skills error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating skills section'
    });
  }
};

// @desc    Update contact section
// @route   PUT /api/portfolio/contact
// @access  Private/Admin
const updateContact = async (req, res) => {
  try {
    const portfolio = await Portfolio.getPortfolio();
    
    const { title, email, phone, location, socialLinks } = req.body;
    
    // Update contact section
    if (title) portfolio.contact.title = title;
    if (email) portfolio.contact.email = email;
    if (phone !== undefined) portfolio.contact.phone = phone;
    if (location !== undefined) portfolio.contact.location = location;
    if (socialLinks) {
      portfolio.contact.socialLinks = {
        ...portfolio.contact.socialLinks,
        ...socialLinks
      };
    }
    
    await portfolio.save();
    
    res.json({
      success: true,
      message: 'Contact section updated successfully',
      data: portfolio.contact
    });
  } catch (error) {
    console.error('Update contact error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating contact section'
    });
  }
};

// @desc    Update section visibility
// @route   PUT /api/portfolio/visibility
// @access  Private/Admin
const updateSectionVisibility = async (req, res) => {
  try {
    const portfolio = await Portfolio.getPortfolio();
    const { section, isVisible } = req.body;
    
    if (!portfolio.sectionVisibility.hasOwnProperty(section)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid section name'
      });
    }
    
    portfolio.sectionVisibility[section] = isVisible;
    await portfolio.save();
    
    res.json({
      success: true,
      message: `${section} section visibility updated successfully`,
      data: portfolio.sectionVisibility
    });
  } catch (error) {
    console.error('Update section visibility error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating section visibility'
    });
  }
};

// @desc    Update SEO settings
// @route   PUT /api/portfolio/seo
// @access  Private/Admin
const updateSEO = async (req, res) => {
  try {
    const portfolio = await Portfolio.getPortfolio();
    const { title, description, keywords } = req.body;
    
    if (title) portfolio.seo.title = title;
    if (description) portfolio.seo.description = description;
    if (keywords) portfolio.seo.keywords = keywords;
    
    await portfolio.save();
    
    res.json({
      success: true,
      message: 'SEO settings updated successfully',
      data: portfolio.seo
    });
  } catch (error) {
    console.error('Update SEO error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating SEO settings'
    });
  }
};

// @desc    Update theme settings
// @route   PUT /api/portfolio/theme
// @access  Private/Admin
const updateTheme = async (req, res) => {
  try {
    const portfolio = await Portfolio.getPortfolio();
    const { primaryColor, accentColor, darkMode } = req.body;
    
    if (primaryColor) portfolio.theme.primaryColor = primaryColor;
    if (accentColor) portfolio.theme.accentColor = accentColor;
    if (darkMode !== undefined) portfolio.theme.darkMode = darkMode;
    
    await portfolio.save();
    
    res.json({
      success: true,
      message: 'Theme settings updated successfully',
      data: portfolio.theme
    });
  } catch (error) {
    console.error('Update theme error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating theme settings'
    });
  }
};

// @desc    Get portfolio statistics
// @route   GET /api/portfolio/stats
// @access  Private/Admin
const getPortfolioStats = async (req, res) => {
  try {
    const portfolio = await Portfolio.getPortfolio();
    
    const stats = {
      sectionsVisible: Object.values(portfolio.sectionVisibility).filter(Boolean).length,
      totalSections: Object.keys(portfolio.sectionVisibility).length,
      skillCategories: portfolio.skills.categories.length,
      totalSkills: portfolio.skills.categories.reduce((total, cat) => total + cat.skills.length, 0),
      lastUpdated: portfolio.updatedAt,
      createdAt: portfolio.createdAt
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get portfolio stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching portfolio statistics'
    });
  }
};

module.exports = {
  getPortfolio,
  getPortfolioAdmin,
  updateAbout,
  updateSkills,
  updateContact,
  updateSectionVisibility,
  updateSEO,
  updateTheme,
  getPortfolioStats
};
