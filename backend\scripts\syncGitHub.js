const mongoose = require('mongoose');
const GitHubSync = require('../utils/githubSync');
require('dotenv').config();

const runSync = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Initialize GitHub sync
    const githubSync = new GitHubSync();

    // Run the sync
    const result = await githubSync.syncProjects();

    if (result.success) {
      console.log('\n🎉 GitHub sync completed successfully!');
    } else {
      console.log('\n❌ GitHub sync failed');
    }

  } catch (error) {
    console.error('Sync script failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
    process.exit(0);
  }
};

// Run sync if this file is executed directly
if (require.main === module) {
  runSync();
}

module.exports = runSync;
