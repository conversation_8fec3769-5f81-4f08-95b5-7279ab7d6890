const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Import database connection
const connectDB = require('./config/database');

// Import middleware
const { errorHandler, notFound } = require('./middleware/errorHandler');

// Import routes
const authRoutes = require('./routes/auth');
const portfolioRoutes = require('./routes/portfolio');
const projectRoutes = require('./routes/projects');
const messageRoutes = require('./routes/messages');

// Connect to database
connectDB();

const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" },
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "http:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.github.com"]
    }
  }
}));

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5173',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:5173',
      process.env.FRONTEND_URL
    ].filter(Boolean);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware (development only)
if (process.env.NODE_ENV === 'development') {
  app.use((req, res, next) => {
    console.log(`${req.method} ${req.path} - ${req.ip}`);
    next();
  });
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/portfolio', portfolioRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/messages', messageRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Portfolio API Server',
    version: '1.0.0',
    documentation: '/api/docs',
    endpoints: {
      auth: '/api/auth',
      portfolio: '/api/portfolio',
      projects: '/api/projects',
      messages: '/api/messages'
    }
  });
});

// API documentation endpoint
app.get('/api/docs', (req, res) => {
  res.json({
    success: true,
    message: 'Portfolio API Documentation',
    version: '1.0.0',
    endpoints: {
      authentication: {
        login: 'POST /api/auth/login',
        register: 'POST /api/auth/register (Admin only)',
        profile: 'GET /api/auth/me',
        updateProfile: 'PUT /api/auth/me',
        changePassword: 'PUT /api/auth/change-password',
        logout: 'POST /api/auth/logout',
        verify: 'GET /api/auth/verify'
      },
      portfolio: {
        getPublic: 'GET /api/portfolio',
        getAdmin: 'GET /api/portfolio/admin (Admin only)',
        updateAbout: 'PUT /api/portfolio/about (Admin only)',
        updateSkills: 'PUT /api/portfolio/skills (Admin only)',
        updateContact: 'PUT /api/portfolio/contact (Admin only)',
        updateVisibility: 'PUT /api/portfolio/visibility (Admin only)',
        updateSEO: 'PUT /api/portfolio/seo (Admin only)',
        updateTheme: 'PUT /api/portfolio/theme (Admin only)',
        getStats: 'GET /api/portfolio/stats (Admin only)'
      },
      projects: {
        getAll: 'GET /api/projects',
        getFeatured: 'GET /api/projects/featured',
        getOne: 'GET /api/projects/:id',
        trackClick: 'POST /api/projects/:id/click',
        getAllAdmin: 'GET /api/projects/admin/all (Admin only)',
        create: 'POST /api/projects (Admin only)',
        update: 'PUT /api/projects/:id (Admin only)',
        delete: 'DELETE /api/projects/:id (Admin only)',
        toggleVisibility: 'PATCH /api/projects/:id/visibility (Admin only)',
        toggleFeatured: 'PATCH /api/projects/:id/featured (Admin only)'
      },
      messages: {
        create: 'POST /api/messages',
        getAll: 'GET /api/messages (Admin only)',
        getStats: 'GET /api/messages/stats (Admin only)',
        getOne: 'GET /api/messages/:id (Admin only)',
        updateStatus: 'PATCH /api/messages/:id/status (Admin only)',
        toggleStar: 'PATCH /api/messages/:id/star (Admin only)',
        reply: 'POST /api/messages/:id/reply (Admin only)',
        markSpam: 'PATCH /api/messages/:id/spam (Admin only)',
        delete: 'DELETE /api/messages/:id (Admin only)'
      }
    }
  });
});

// Handle 404 errors
app.use(notFound);

// Error handling middleware (must be last)
app.use(errorHandler);

const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  console.log(`
🚀 Portfolio API Server is running!
📍 Environment: ${process.env.NODE_ENV || 'development'}
🌐 Port: ${PORT}
📚 Documentation: http://localhost:${PORT}/api/docs
🏥 Health Check: http://localhost:${PORT}/health
  `);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.log('Unhandled Rejection at:', promise, 'reason:', err);
  // Close server & exit process
  server.close(() => {
    process.exit(1);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.log('Uncaught Exception:', err);
  process.exit(1);
});

module.exports = app;
