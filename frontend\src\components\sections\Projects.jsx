import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ExternalLink, Github, Eye, Filter, X } from 'lucide-react';
import { usePortfolio } from '../../contexts/PortfolioContext';
import LoadingSpinner from '../LoadingSpinner';

const Projects = () => {
  const { featuredProjects, fetchProjects, loading } = usePortfolio();
  const [allProjects, setAllProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showAll, setShowAll] = useState(false);

  const categories = [
    { id: 'all', name: 'All Projects' },
    { id: 'web-app', name: 'Web Apps' },
    { id: 'mobile-app', name: 'Mobile Apps' },
    { id: 'api', name: 'APIs' },
    { id: 'library', name: 'Libraries' },
    { id: 'tool', name: 'Tools' },
    { id: 'other', name: 'Other' }
  ];

  useEffect(() => {
    if (showAll) {
      loadAllProjects();
    }
  }, [showAll]);

  useEffect(() => {
    filterProjects();
  }, [allProjects, selectedCategory]);

  const loadAllProjects = async () => {
    const result = await fetchProjects();
    if (result?.projects) {
      setAllProjects(result.projects);
    }
  };

  const filterProjects = () => {
    if (selectedCategory === 'all') {
      setFilteredProjects(allProjects);
    } else {
      setFilteredProjects(allProjects.filter(project => project.category === selectedCategory));
    }
  };

  const handleCategoryChange = (categoryId) => {
    setSelectedCategory(categoryId);
  };

  const trackProjectClick = async (projectId) => {
    try {
      await fetch(`${import.meta.env.VITE_API_URL}/projects/${projectId}/click`, {
        method: 'POST'
      });
    } catch (error) {
      console.error('Failed to track click:', error);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  };

  const projectsToShow = showAll ? filteredProjects : featuredProjects;

  return (
    <section id="projects" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-1/3 w-96 h-96 bg-neon-purple/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 left-1/3 w-96 h-96 bg-neon-blue/5 rounded-full blur-3xl" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">
                {showAll ? 'All Projects' : 'Featured Projects'}
              </span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
              {showAll 
                ? 'Explore my complete portfolio of projects and experiments'
                : 'A showcase of my best work and most impactful projects'
              }
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-purple mx-auto rounded-full" />
          </motion.div>

          {/* View Toggle */}
          <motion.div variants={itemVariants} className="flex justify-center mb-12">
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowAll(!showAll)}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                  showAll 
                    ? 'btn-secondary' 
                    : 'btn-primary'
                }`}
              >
                {showAll ? (
                  <>
                    <X className="w-5 h-5 mr-2" />
                    Show Featured Only
                  </>
                ) : (
                  <>
                    <Eye className="w-5 h-5 mr-2" />
                    View All Projects
                  </>
                )}
              </motion.button>
            </div>
          </motion.div>

          {/* Category Filter (only show when viewing all projects) */}
          <AnimatePresence>
            {showAll && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mb-12"
              >
                <div className="flex flex-wrap justify-center gap-4">
                  {categories.map((category) => (
                    <motion.button
                      key={category.id}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleCategoryChange(category.id)}
                      className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                        selectedCategory === category.id
                          ? 'bg-neon-blue text-white'
                          : 'glass text-gray-300 hover:bg-white/10'
                      }`}
                    >
                      <Filter className="w-4 h-4 mr-2 inline" />
                      {category.name}
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Projects Grid */}
          {loading ? (
            <div className="flex justify-center py-20">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <motion.div
              variants={containerVariants}
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              <AnimatePresence mode="wait">
                {projectsToShow.map((project, index) => (
                  <motion.div
                    key={project._id}
                    variants={itemVariants}
                    layout
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.3 }}
                    className="card-hover group relative overflow-hidden"
                  >
                    {/* Project Image */}
                    <div className="relative h-48 mb-6 rounded-lg overflow-hidden bg-gradient-to-br from-neon-blue/20 to-neon-purple/20">
                      {project.primaryImage ? (
                        <img
                          src={project.primaryImage.url}
                          alt={project.title}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Github size={48} className="text-neon-blue/50" />
                        </div>
                      )}
                      
                      {/* Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      
                      {/* Featured Badge */}
                      {project.isFeatured && (
                        <div className="absolute top-4 left-4 px-3 py-1 bg-neon-blue/90 text-white text-xs font-medium rounded-full">
                          Featured
                        </div>
                      )}
                    </div>

                    {/* Project Info */}
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-xl font-bold text-white mb-2 group-hover:text-neon-blue transition-colors">
                          {project.title}
                        </h3>
                        <p className="text-gray-400 text-sm leading-relaxed">
                          {project.shortDescription || project.description}
                        </p>
                      </div>

                      {/* Technologies */}
                      {project.technologies && project.technologies.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {project.technologies.slice(0, 3).map((tech, techIndex) => (
                            <span
                              key={techIndex}
                              className="px-2 py-1 text-xs bg-gray-800 text-gray-300 rounded-md"
                            >
                              {tech.name}
                            </span>
                          ))}
                          {project.technologies.length > 3 && (
                            <span className="px-2 py-1 text-xs bg-gray-800 text-gray-400 rounded-md">
                              +{project.technologies.length - 3} more
                            </span>
                          )}
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex space-x-3 pt-4">
                        {project.liveUrl && (
                          <motion.a
                            href={project.liveUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            onClick={() => trackProjectClick(project._id)}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="flex-1 btn-primary text-center text-sm py-2"
                          >
                            <ExternalLink className="w-4 h-4 mr-2 inline" />
                            Live Demo
                          </motion.a>
                        )}
                        
                        <motion.a
                          href={project.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          onClick={() => trackProjectClick(project._id)}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className={`${project.liveUrl ? 'px-4' : 'flex-1'} btn-secondary text-center text-sm py-2`}
                        >
                          <Github className="w-4 h-4 mr-2 inline" />
                          {project.liveUrl ? '' : 'View Code'}
                        </motion.a>
                      </div>
                    </div>

                    {/* Hover Effects */}
                    <motion.div
                      className="absolute -top-2 -right-2 w-4 h-4 bg-neon-pink rounded-full opacity-0 group-hover:opacity-60 transition-opacity duration-300"
                      animate={{
                        y: [0, -5, 0],
                        rotate: [0, 180, 360]
                      }}
                      transition={{
                        duration: 4,
                        repeat: Infinity,
                        ease: 'easeInOut'
                      }}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            </motion.div>
          )}

          {/* Empty State */}
          {!loading && projectsToShow.length === 0 && (
            <motion.div
              variants={itemVariants}
              className="text-center py-20"
            >
              <Github size={64} className="text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">
                No projects found
              </h3>
              <p className="text-gray-500">
                {showAll 
                  ? 'No projects match the selected category.'
                  : 'No featured projects available at the moment.'
                }
              </p>
            </motion.div>
          )}
        </motion.div>
      </div>
    </section>
  );
};

export default Projects;
