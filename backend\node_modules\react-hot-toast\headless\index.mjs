var V=e=>typeof e=="function",m=(e,t)=>V(e)?e(t):e;var P=(()=>{let e=0;return()=>(++e).toString()})(),B=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})();import{useEffect as x,useState as _,useRef as v}from"react";var I=20;var b=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,I)};case 1:return{...e,toasts:e.toasts.map(o=>o.id===t.toast.id?{...o,...t.toast}:o)};case 2:let{toast:r}=t;return b(e,{type:e.toasts.find(o=>o.id===r.id)?1:0,toast:r});case 3:let{toastId:n}=t;return{...e,toasts:e.toasts.map(o=>o.id===n||n===void 0?{...o,dismissed:!0,visible:!1}:o)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(o=>o.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let i=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(o=>({...o,pauseDuration:o.pauseDuration+i}))}}},y=[],l={toasts:[],pausedAt:void 0},c=e=>{l=b(l,e),y.forEach(t=>{t(l)})},M={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},g=(e={})=>{let[t,r]=_(l),n=v(l);x(()=>(n.current!==l&&r(l),y.push(r),()=>{let o=y.indexOf(r);o>-1&&y.splice(o,1)}),[]);let i=t.toasts.map(o=>{var T,a,d;return{...e,...e[o.type],...o,removeDelay:o.removeDelay||((T=e[o.type])==null?void 0:T.removeDelay)||(e==null?void 0:e.removeDelay),duration:o.duration||((a=e[o.type])==null?void 0:a.duration)||(e==null?void 0:e.duration)||M[o.type],style:{...e.style,...(d=e[o.type])==null?void 0:d.style,...o.style}}});return{...t,toasts:i}};var F=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(r==null?void 0:r.id)||P()}),p=e=>(t,r)=>{let n=F(t,e,r);return c({type:2,toast:n}),n.id},s=(e,t)=>p("blank")(e,t);s.error=p("error");s.success=p("success");s.loading=p("loading");s.custom=p("custom");s.dismiss=e=>{c({type:3,toastId:e})};s.remove=e=>c({type:4,toastId:e});s.promise=(e,t,r)=>{let n=s.loading(t.loading,{...r,...r==null?void 0:r.loading});return typeof e=="function"&&(e=e()),e.then(i=>{let o=t.success?m(t.success,i):void 0;return o?s.success(o,{id:n,...r,...r==null?void 0:r.success}):s.dismiss(n),i}).catch(i=>{let o=t.error?m(t.error,i):void 0;o?s.error(o,{id:n,...r,...r==null?void 0:r.error}):s.dismiss(n)}),e};import{useEffect as E,useCallback as R}from"react";var w=(e,t)=>{c({type:1,toast:{id:e,height:t}})},N=()=>{c({type:5,time:Date.now()})},f=new Map,k=1e3,C=(e,t=k)=>{if(f.has(e))return;let r=setTimeout(()=>{f.delete(e),c({type:4,toastId:e})},t);f.set(e,r)},H=e=>{let{toasts:t,pausedAt:r}=g(e);E(()=>{if(r)return;let o=Date.now(),T=t.map(a=>{if(a.duration===1/0)return;let d=(a.duration||0)+a.pauseDuration-(o-a.createdAt);if(d<0){a.visible&&s.dismiss(a.id);return}return setTimeout(()=>s.dismiss(a.id),d)});return()=>{T.forEach(a=>a&&clearTimeout(a))}},[t,r]);let n=R(()=>{r&&c({type:6,time:Date.now()})},[r]),i=R((o,T)=>{let{reverseOrder:a=!1,gutter:d=8,defaultPosition:D}=T||{},S=t.filter(u=>(u.position||D)===(o.position||D)&&u.height),h=S.findIndex(u=>u.id===o.id),O=S.filter((u,A)=>A<h&&u.visible).length;return S.filter(u=>u.visible).slice(...a?[O+1]:[0,O]).reduce((u,A)=>u+(A.height||0)+d,0)},[t]);return E(()=>{t.forEach(o=>{if(o.dismissed)C(o.id,o.removeDelay);else{let T=f.get(o.id);T&&(clearTimeout(T),f.delete(o.id))}})},[t]),{toasts:t,handlers:{updateHeight:w,startPause:N,endPause:n,calculateOffset:i}}};var ie=s;export{ie as default,m as resolveValue,s as toast,H as useToaster,g as useToasterStore};
//# sourceMappingURL=index.mjs.map