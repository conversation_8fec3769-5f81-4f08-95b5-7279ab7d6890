{"name": "portfolio-backend", "version": "1.0.0", "description": "Backend API for <PERSON><PERSON>f <PERSON>'s dynamic portfolio", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init": "node utils/initAdmin.js", "sync-github": "node scripts/syncGitHub.js", "setup": "npm run init && npm run sync-github", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["portfolio", "api", "express", "mongodb"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.3", "multer": "^2.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}