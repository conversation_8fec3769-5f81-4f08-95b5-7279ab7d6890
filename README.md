# 🚀 Dynamic Portfolio with Admin Dashboard (MERN Stack)

A modern, futuristic developer portfolio for **Cherif Bourechache**, featuring a public-facing portfolio and a secure admin dashboard built with the MERN stack.

## ✨ Features

### 🌐 Public Portfolio
- **About Me** section (editable via dashboard)
- **Skills** showcase with modern animations
- **Projects** section with GitHub integration + custom projects
- **Contact** form with message storage
- **Responsive** design with glassmorphism effects
- **Dark mode** with neon glows and animations

### 🔐 Admin Dashboard
- **Secure JWT authentication**
- **Content management** for all portfolio sections
- **Project CRUD** operations
- **Message management** from contact form
- **Section visibility** controls
- **Real-time updates**

## 🛠️ Tech Stack

- **Frontend**: React + Vite + Tailwind CSS + Framer Motion
- **Backend**: Node.js + Express.js + MongoDB
- **Authentication**: JWT-based secure login
- **Database**: MongoDB with Mongoose ODM
- **Styling**: Tailwind CSS with custom glassmorphism theme
- **Icons**: Lucide React
- **Animations**: Framer Motion

## 📁 Project Structure

```
portfolio-mern/
├── frontend/          # React frontend (Vite)
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── styles/
│   └── public/
├── backend/           # Express.js API
│   ├── models/        # MongoDB schemas
│   ├── routes/        # API endpoints
│   ├── middleware/    # Auth & validation
│   ├── controllers/   # Business logic
│   ├── config/        # Database config
│   └── utils/         # Helper functions
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v18+)
- MongoDB Atlas account
- Git

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd portfolio-mern
```

2. **Install Frontend Dependencies**
```bash
cd frontend
npm install
```

3. **Install Backend Dependencies**
```bash
cd ../backend
npm install
```

4. **Environment Setup**
Create `.env` file in backend directory:
```env
PORT=5000
MONGODB_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret_key
NODE_ENV=development
GITHUB_TOKEN=your_github_personal_access_token
```

5. **Run the Application**

Backend (Terminal 1):
```bash
cd backend
npm run dev
```

Frontend (Terminal 2):
```bash
cd frontend
npm run dev
```

## 🌟 Key Features Implementation

- **Glassmorphism UI**: Custom Tailwind classes for modern glass effects
- **GitHub Integration**: Automatic project fetching from GitHub API
- **JWT Authentication**: Secure admin access with token-based auth
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Smooth Animations**: Framer Motion for engaging user experience
- **Contact Form**: Direct message storage in MongoDB
- **Admin Dashboard**: Full CRUD operations for portfolio content

## 📱 Social Links

- **GitHub**: [https://github.com/chxb07](https://github.com/chxb07)
- **LinkedIn**: [https://linkedin.com/in/cherif-bourechache-3099432a8](https://linkedin.com/in/cherif-bourechache-3099432a8)

## 📄 License

MIT License - feel free to use this project as a template for your own portfolio!

---

Built with ❤️ by **Cherif Bourechache**
