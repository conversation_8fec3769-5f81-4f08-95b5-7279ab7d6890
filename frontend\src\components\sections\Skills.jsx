import { motion } from 'framer-motion';
import { Code2, Palette, Database, Tool } from 'lucide-react';

const Skills = ({ data }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  };

  const categoryIcons = {
    'Frontend Development': Code2,
    'Backend Development': Database,
    'Design': Palette,
    'Tools & Technologies': Tool,
    'Tools': Tool,
    'Technologies': Tool
  };

  const getSkillColor = (level) => {
    if (level >= 80) return 'from-neon-green to-neon-blue';
    if (level >= 60) return 'from-neon-blue to-neon-purple';
    if (level >= 40) return 'from-neon-purple to-neon-pink';
    return 'from-neon-pink to-red-400';
  };

  const defaultSkills = [
    {
      name: 'Frontend Development',
      skills: [
        { name: 'React', level: 90 },
        { name: 'JavaScript', level: 85 },
        { name: 'TypeScript', level: 80 },
        { name: 'HTML5', level: 95 },
        { name: 'CSS3', level: 90 },
        { name: 'Tailwind CSS', level: 85 }
      ]
    },
    {
      name: 'Backend Development',
      skills: [
        { name: 'Node.js', level: 80 },
        { name: 'Express.js', level: 75 },
        { name: 'MongoDB', level: 70 },
        { name: 'REST APIs', level: 85 }
      ]
    },
    {
      name: 'Tools & Technologies',
      skills: [
        { name: 'Git', level: 85 },
        { name: 'VS Code', level: 90 },
        { name: 'Figma', level: 70 },
        { name: 'Postman', level: 80 }
      ]
    }
  ];

  const skillCategories = data?.categories || defaultSkills;

  return (
    <section id="skills" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-neon-green/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-neon-pink/5 rounded-full blur-3xl" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">
                {data?.title || 'Skills & Technologies'}
              </span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
              Here are the technologies and tools I work with to bring ideas to life
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-purple mx-auto rounded-full" />
          </motion.div>

          {/* Skills Grid */}
          <div className="grid lg:grid-cols-3 gap-8">
            {skillCategories.map((category, categoryIndex) => {
              const IconComponent = categoryIcons[category.name] || Code2;
              
              return (
                <motion.div
                  key={category.name}
                  variants={itemVariants}
                  className="card-hover group"
                >
                  {/* Category Header */}
                  <div className="flex items-center mb-6">
                    <div className="p-3 glass-strong rounded-lg mr-4 group-hover:bg-neon-blue/20 transition-colors">
                      <IconComponent className="w-6 h-6 text-neon-blue" />
                    </div>
                    <h3 className="text-xl font-semibold text-white">
                      {category.name}
                    </h3>
                  </div>

                  {/* Skills List */}
                  <div className="space-y-4">
                    {category.skills.map((skill, skillIndex) => (
                      <motion.div
                        key={skill.name}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ 
                          delay: categoryIndex * 0.1 + skillIndex * 0.05,
                          duration: 0.5 
                        }}
                        className="space-y-2"
                      >
                        {/* Skill Name and Level */}
                        <div className="flex justify-between items-center">
                          <span className="text-gray-300 font-medium">
                            {skill.name}
                          </span>
                          <span className="text-sm text-gray-400">
                            {skill.level}%
                          </span>
                        </div>

                        {/* Progress Bar */}
                        <div className="relative h-2 bg-gray-800 rounded-full overflow-hidden">
                          <motion.div
                            initial={{ width: 0 }}
                            whileInView={{ width: `${skill.level}%` }}
                            transition={{ 
                              delay: categoryIndex * 0.1 + skillIndex * 0.05 + 0.2,
                              duration: 1,
                              ease: 'easeOut'
                            }}
                            className={`h-full bg-gradient-to-r ${getSkillColor(skill.level)} rounded-full relative`}
                          >
                            {/* Glow Effect */}
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
                          </motion.div>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Floating Skill Icons */}
                  <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <motion.div
                      animate={{
                        y: [0, -5, 0],
                        rotate: [0, 5, 0]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: 'easeInOut'
                      }}
                      className="w-4 h-4 bg-neon-blue rounded-full"
                    />
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Additional Info */}
          <motion.div
            variants={itemVariants}
            className="mt-16 text-center"
          >
            <div className="glass rounded-2xl p-8 max-w-4xl mx-auto">
              <h3 className="text-2xl font-bold text-white mb-4">
                Always Learning
              </h3>
              <p className="text-gray-400 leading-relaxed">
                Technology evolves rapidly, and I'm committed to staying current with the latest trends and best practices. 
                I regularly explore new frameworks, tools, and methodologies to enhance my development skills and deliver 
                cutting-edge solutions.
              </p>
              
              {/* Learning Indicators */}
              <div className="flex flex-wrap justify-center gap-4 mt-6">
                {['Next.js', 'GraphQL', 'Docker', 'AWS'].map((tech, index) => (
                  <motion.span
                    key={tech}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="px-4 py-2 glass-strong rounded-full text-sm text-neon-blue border border-neon-blue/30"
                  >
                    Currently Learning: {tech}
                  </motion.span>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Skills;
