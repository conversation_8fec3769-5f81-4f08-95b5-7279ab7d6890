const mongoose = require('mongoose');

const projectSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Project title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Project description is required'],
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  shortDescription: {
    type: String,
    trim: true,
    maxlength: [200, 'Short description cannot exceed 200 characters']
  },
  
  // Project URLs
  githubUrl: {
    type: String,
    required: [true, 'GitHub URL is required'],
    match: [/^https:\/\/github\.com\/.*/, 'Please enter a valid GitHub URL']
  },
  liveUrl: {
    type: String,
    default: null,
    validate: {
      validator: function(v) {
        return !v || /^https?:\/\/.*/.test(v);
      },
      message: 'Please enter a valid URL'
    }
  },
  
  // Project Images
  images: [{
    url: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: ''
    },
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  
  // Technologies Used
  technologies: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    category: {
      type: String,
      enum: ['frontend', 'backend', 'database', 'tool', 'framework', 'library', 'other'],
      default: 'other'
    },
    icon: {
      type: String,
      default: null
    }
  }],
  
  // Project Features
  features: [{
    type: String,
    trim: true,
    maxlength: [200, 'Feature description cannot exceed 200 characters']
  }],
  
  // Project Status and Metadata
  status: {
    type: String,
    enum: ['planning', 'in-progress', 'completed', 'on-hold', 'archived'],
    default: 'completed'
  },
  priority: {
    type: Number,
    min: [1, 'Priority must be at least 1'],
    max: [10, 'Priority cannot exceed 10'],
    default: 5
  },
  category: {
    type: String,
    enum: ['web-app', 'mobile-app', 'desktop-app', 'api', 'library', 'tool', 'game', 'other'],
    default: 'web-app'
  },
  
  // Project Metadata
  metadata: {
    primaryLanguage: {
      type: String,
      default: null
    },
    repositorySize: {
      type: String,
      default: null
    },
    lastCommit: {
      type: Date,
      default: null
    },
    license: {
      type: String,
      default: null
    }
  },
  
  // Display Settings
  isVisible: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  showInPortfolio: {
    type: Boolean,
    default: true
  },
  
  // Dates
  startDate: {
    type: Date,
    default: null
  },
  endDate: {
    type: Date,
    default: null
  },
  
  // Additional Metadata
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  difficulty: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced', 'expert'],
    default: 'intermediate'
  },
  teamSize: {
    type: Number,
    min: [1, 'Team size must be at least 1'],
    default: 1
  },
  
  // Analytics
  views: {
    type: Number,
    default: 0
  },
  clicks: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes for better query performance
projectSchema.index({ status: 1, isVisible: 1 });
projectSchema.index({ category: 1, isVisible: 1 });
projectSchema.index({ isFeatured: 1, isVisible: 1 });
projectSchema.index({ priority: -1, createdAt: -1 });
projectSchema.index({ tags: 1 });

// Virtual for primary image
projectSchema.virtual('primaryImage').get(function() {
  const primary = this.images.find(img => img.isPrimary);
  return primary || (this.images.length > 0 ? this.images[0] : null);
});

// Virtual for project duration
projectSchema.virtual('duration').get(function() {
  if (!this.startDate || !this.endDate) return null;
  
  const diffTime = Math.abs(this.endDate - this.startDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 30) return `${diffDays} days`;
  if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months`;
  return `${Math.ceil(diffDays / 365)} years`;
});

// Static method to get featured projects
projectSchema.statics.getFeatured = function(limit = 6) {
  return this.find({ 
    isFeatured: true, 
    isVisible: true, 
    showInPortfolio: true 
  })
  .sort({ priority: -1, createdAt: -1 })
  .limit(limit);
};

// Static method to get projects by category
projectSchema.statics.getByCategory = function(category, limit = null) {
  const query = this.find({ 
    category, 
    isVisible: true, 
    showInPortfolio: true 
  }).sort({ priority: -1, createdAt: -1 });
  
  return limit ? query.limit(limit) : query;
};

// Instance method to increment views
projectSchema.methods.incrementViews = function() {
  this.views += 1;
  return this.save();
};

// Instance method to increment clicks
projectSchema.methods.incrementClicks = function() {
  this.clicks += 1;
  return this.save();
};

// Pre-save middleware to ensure only one primary image
projectSchema.pre('save', function(next) {
  if (this.isModified('images')) {
    const primaryImages = this.images.filter(img => img.isPrimary);
    
    if (primaryImages.length > 1) {
      // Keep only the first primary image
      this.images.forEach((img, index) => {
        if (index > 0 && img.isPrimary) {
          img.isPrimary = false;
        }
      });
    } else if (primaryImages.length === 0 && this.images.length > 0) {
      // Set first image as primary if none is set
      this.images[0].isPrimary = true;
    }
  }
  next();
});

module.exports = mongoose.model('Project', projectSchema);
