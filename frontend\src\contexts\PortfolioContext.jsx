import { createContext, useContext, useReducer, useEffect } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';

const PortfolioContext = createContext();

// Portfolio reducer
const portfolioReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      };
    case 'SET_PORTFOLIO':
      return {
        ...state,
        portfolio: action.payload,
        loading: false,
        error: null
      };
    case 'SET_PROJECTS':
      return {
        ...state,
        projects: action.payload,
        loading: false
      };
    case 'SET_FEATURED_PROJECTS':
      return {
        ...state,
        featuredProjects: action.payload
      };
    case 'SET_MESSAGES':
      return {
        ...state,
        messages: action.payload,
        loading: false
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        loading: false
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null
      };
    case 'UPDATE_PORTFOLIO_SECTION':
      return {
        ...state,
        portfolio: {
          ...state.portfolio,
          [action.payload.section]: action.payload.data
        }
      };
    case 'ADD_PROJECT':
      return {
        ...state,
        projects: [action.payload, ...state.projects]
      };
    case 'UPDATE_PROJECT':
      return {
        ...state,
        projects: state.projects.map(project =>
          project._id === action.payload._id ? action.payload : project
        )
      };
    case 'DELETE_PROJECT':
      return {
        ...state,
        projects: state.projects.filter(project => project._id !== action.payload)
      };
    case 'UPDATE_MESSAGE':
      return {
        ...state,
        messages: state.messages.map(message =>
          message._id === action.payload._id ? action.payload : message
        )
      };
    case 'DELETE_MESSAGE':
      return {
        ...state,
        messages: state.messages.filter(message => message._id !== action.payload)
      };
    default:
      return state;
  }
};

// Initial state
const initialState = {
  portfolio: null,
  projects: [],
  featuredProjects: [],
  messages: [],
  loading: false,
  error: null
};

// Portfolio provider component
export const PortfolioProvider = ({ children }) => {
  const [state, dispatch] = useReducer(portfolioReducer, initialState);

  // Fetch portfolio data
  const fetchPortfolio = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const response = await axios.get('/portfolio');
      
      if (response.data.success) {
        dispatch({
          type: 'SET_PORTFOLIO',
          payload: response.data.data
        });
      } else {
        throw new Error(response.data.message || 'Failed to fetch portfolio');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch portfolio';
      dispatch({
        type: 'SET_ERROR',
        payload: errorMessage
      });
      console.error('Fetch portfolio error:', error);
    }
  };

  // Fetch projects
  const fetchProjects = async (params = {}) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const response = await axios.get('/projects', { params });
      
      if (response.data.success) {
        dispatch({
          type: 'SET_PROJECTS',
          payload: response.data.data.projects
        });
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to fetch projects');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch projects';
      dispatch({
        type: 'SET_ERROR',
        payload: errorMessage
      });
      console.error('Fetch projects error:', error);
    }
  };

  // Fetch featured projects
  const fetchFeaturedProjects = async (limit = 6) => {
    try {
      const response = await axios.get(`/projects/featured?limit=${limit}`);
      
      if (response.data.success) {
        dispatch({
          type: 'SET_FEATURED_PROJECTS',
          payload: response.data.data
        });
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to fetch featured projects');
      }
    } catch (error) {
      console.error('Fetch featured projects error:', error);
    }
  };

  // Send contact message
  const sendMessage = async (messageData) => {
    try {
      const response = await axios.post('/messages', messageData);
      
      if (response.data.success) {
        toast.success('Message sent successfully!');
        return { success: true };
      } else {
        throw new Error(response.data.message || 'Failed to send message');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to send message';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Admin functions
  const updatePortfolioSection = async (section, data) => {
    try {
      const response = await axios.put(`/portfolio/${section}`, data);
      
      if (response.data.success) {
        dispatch({
          type: 'UPDATE_PORTFOLIO_SECTION',
          payload: { section, data: response.data.data }
        });
        toast.success(`${section} section updated successfully!`);
        return { success: true };
      } else {
        throw new Error(response.data.message || 'Failed to update section');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to update section';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const createProject = async (projectData) => {
    try {
      const response = await axios.post('/projects', projectData);
      
      if (response.data.success) {
        dispatch({
          type: 'ADD_PROJECT',
          payload: response.data.data
        });
        toast.success('Project created successfully!');
        return { success: true, data: response.data.data };
      } else {
        throw new Error(response.data.message || 'Failed to create project');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to create project';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const updateProject = async (projectId, projectData) => {
    try {
      const response = await axios.put(`/projects/${projectId}`, projectData);
      
      if (response.data.success) {
        dispatch({
          type: 'UPDATE_PROJECT',
          payload: response.data.data
        });
        toast.success('Project updated successfully!');
        return { success: true, data: response.data.data };
      } else {
        throw new Error(response.data.message || 'Failed to update project');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to update project';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const deleteProject = async (projectId) => {
    try {
      const response = await axios.delete(`/projects/${projectId}`);
      
      if (response.data.success) {
        dispatch({
          type: 'DELETE_PROJECT',
          payload: projectId
        });
        toast.success('Project deleted successfully!');
        return { success: true };
      } else {
        throw new Error(response.data.message || 'Failed to delete project');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to delete project';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Fetch messages (admin)
  const fetchMessages = async (params = {}) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const response = await axios.get('/messages', { params });
      
      if (response.data.success) {
        dispatch({
          type: 'SET_MESSAGES',
          payload: response.data.data.messages
        });
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to fetch messages');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch messages';
      dispatch({
        type: 'SET_ERROR',
        payload: errorMessage
      });
      console.error('Fetch messages error:', error);
    }
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Load initial data
  useEffect(() => {
    fetchPortfolio();
    fetchFeaturedProjects();
  }, []);

  const value = {
    ...state,
    fetchPortfolio,
    fetchProjects,
    fetchFeaturedProjects,
    sendMessage,
    updatePortfolioSection,
    createProject,
    updateProject,
    deleteProject,
    fetchMessages,
    clearError
  };

  return (
    <PortfolioContext.Provider value={value}>
      {children}
    </PortfolioContext.Provider>
  );
};

// Custom hook to use portfolio context
export const usePortfolio = () => {
  const context = useContext(PortfolioContext);
  if (!context) {
    throw new Error('usePortfolio must be used within a PortfolioProvider');
  }
  return context;
};
