import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Mail, 
  Phone, 
  Building, 
  Calendar, 
  Star, 
  Reply, 
  Archive, 
  Trash2, 
  AlertTriangle,
  Send,
  User
} from 'lucide-react';
import axios from 'axios';
import toast from 'react-hot-toast';
import LoadingSpinner from '../LoadingSpinner';

const MessageModal = ({ 
  isOpen, 
  onClose, 
  message, 
  onStatusChange, 
  onToggleStar, 
  onDelete, 
  onMarkAsSpam 
}) => {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [replyMessage, setReplyMessage] = useState('');
  const [sending, setSending] = useState(false);

  if (!isOpen || !message) return null;

  const handleReply = async () => {
    if (!replyMessage.trim()) return;

    setSending(true);
    try {
      const response = await axios.post(`/messages/${message._id}/reply`, {
        responseMessage: replyMessage
      });

      if (response.data.success) {
        toast.success('Reply sent successfully!');
        onStatusChange(message._id, 'replied');
        setShowReplyForm(false);
        setReplyMessage('');
      }
    } catch (error) {
      toast.error('Failed to send reply');
      console.error('Reply error:', error);
    } finally {
      setSending(false);
    }
  };

  const handleArchive = () => {
    onStatusChange(message._id, 'archived');
    onClose();
  };

  const handleDelete = () => {
    onDelete(message._id);
    onClose();
  };

  const handleMarkAsSpam = () => {
    onMarkAsSpam(message._id);
    onClose();
  };

  const getStatusColor = (status) => {
    const colors = {
      unread: 'bg-neon-blue/20 text-neon-blue',
      read: 'bg-gray-500/20 text-gray-400',
      replied: 'bg-green-500/20 text-green-400',
      archived: 'bg-yellow-500/20 text-yellow-400'
    };
    return colors[status] || 'bg-gray-500/20 text-gray-400';
  };

  const getTypeColor = (type) => {
    const colors = {
      'project-inquiry': 'bg-neon-purple/20 text-neon-purple',
      'job-opportunity': 'bg-neon-green/20 text-neon-green',
      'collaboration': 'bg-neon-pink/20 text-neon-pink',
      'feedback': 'bg-yellow-500/20 text-yellow-400',
      'general': 'bg-blue-500/20 text-blue-400',
      'other': 'bg-gray-500/20 text-gray-400'
    };
    return colors[type] || 'bg-gray-500/20 text-gray-400';
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          onClick={(e) => e.stopPropagation()}
          className="w-full max-w-4xl max-h-[90vh] overflow-y-auto glass-strong rounded-2xl border border-white/20"
        >
          <div className="p-6">
            {/* Header */}
            <div className="flex items-start justify-between border-b border-white/10 pb-6 mb-6">
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-gradient-to-r from-neon-blue to-neon-purple rounded-full flex items-center justify-center">
                  <span className="text-white text-xl font-bold">
                    {message.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                
                <div>
                  <div className="flex items-center space-x-3 mb-2">
                    <h2 className="text-2xl font-bold text-white">{message.name}</h2>
                    {message.isStarred && (
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-400 mb-3">
                    <span className="flex items-center">
                      <Mail className="w-4 h-4 mr-1" />
                      {message.email}
                    </span>
                    {message.phone && (
                      <span className="flex items-center">
                        <Phone className="w-4 h-4 mr-1" />
                        {message.phone}
                      </span>
                    )}
                    {message.company && (
                      <span className="flex items-center">
                        <Building className="w-4 h-4 mr-1" />
                        {message.company}
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <span className={`px-3 py-1 text-sm rounded-full ${getStatusColor(message.status)}`}>
                      {message.status}
                    </span>
                    <span className={`px-3 py-1 text-sm rounded-full ${getTypeColor(message.type)}`}>
                      {message.type.replace('-', ' ')}
                    </span>
                    <span className="flex items-center text-sm text-gray-400">
                      <Calendar className="w-4 h-4 mr-1" />
                      {new Date(message.createdAt).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              <button
                onClick={onClose}
                className="p-2 glass rounded-lg hover:bg-white/10 transition-colors"
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            </div>

            {/* Message Content */}
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold text-white mb-3">
                  {message.subject}
                </h3>
                <div className="glass rounded-lg p-6">
                  <p className="text-gray-300 leading-relaxed whitespace-pre-wrap">
                    {message.message}
                  </p>
                </div>
              </div>

              {/* Previous Reply */}
              {message.response && message.response.message && (
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">Previous Reply</h4>
                  <div className="glass rounded-lg p-6 border-l-4 border-green-500">
                    <p className="text-gray-300 leading-relaxed whitespace-pre-wrap">
                      {message.response.message}
                    </p>
                    <div className="mt-4 text-sm text-gray-400">
                      Sent on {new Date(message.response.sentAt).toLocaleString()}
                    </div>
                  </div>
                </div>
              )}

              {/* Reply Form */}
              <AnimatePresence>
                {showReplyForm && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-4"
                  >
                    <h4 className="text-lg font-semibold text-white">Send Reply</h4>
                    <div className="space-y-4">
                      <textarea
                        value={replyMessage}
                        onChange={(e) => setReplyMessage(e.target.value)}
                        placeholder="Type your reply..."
                        rows={6}
                        className="textarea"
                      />
                      
                      <div className="flex justify-end space-x-3">
                        <button
                          onClick={() => setShowReplyForm(false)}
                          className="btn-secondary"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleReply}
                          disabled={sending || !replyMessage.trim()}
                          className="btn-primary disabled:opacity-50"
                        >
                          {sending ? (
                            <LoadingSpinner size="sm" className="mr-2" />
                          ) : (
                            <Send className="w-4 h-4 mr-2" />
                          )}
                          {sending ? 'Sending...' : 'Send Reply'}
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Actions */}
            <div className="flex flex-wrap gap-3 pt-6 border-t border-white/10 mt-6">
              {!showReplyForm && (
                <button
                  onClick={() => setShowReplyForm(true)}
                  className="btn-primary"
                >
                  <Reply className="w-4 h-4 mr-2" />
                  Reply
                </button>
              )}

              <button
                onClick={() => onToggleStar(message._id)}
                className={`btn-secondary ${
                  message.isStarred ? 'text-yellow-400' : ''
                }`}
              >
                <Star className={`w-4 h-4 mr-2 ${message.isStarred ? 'fill-current' : ''}`} />
                {message.isStarred ? 'Unstar' : 'Star'}
              </button>

              <button
                onClick={handleArchive}
                className="btn-secondary"
              >
                <Archive className="w-4 h-4 mr-2" />
                Archive
              </button>

              <button
                onClick={handleMarkAsSpam}
                className="btn-secondary text-orange-400 hover:text-orange-300"
              >
                <AlertTriangle className="w-4 h-4 mr-2" />
                Mark as Spam
              </button>

              <button
                onClick={handleDelete}
                className="btn-secondary text-red-400 hover:text-red-300"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </button>
            </div>

            {/* Message Metadata */}
            <div className="mt-6 pt-6 border-t border-white/10">
              <h4 className="text-sm font-semibold text-gray-300 mb-3">Message Details</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">IP Address:</span>
                  <p className="text-gray-300">{message.ipAddress || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-gray-400">User Agent:</span>
                  <p className="text-gray-300 truncate" title={message.userAgent}>
                    {message.userAgent ? message.userAgent.substring(0, 30) + '...' : 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400">Spam Score:</span>
                  <p className="text-gray-300">{message.spamScore || 0}%</p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default MessageModal;
