const mongoose = require('mongoose');
const { User, Portfolio } = require('../models');
require('dotenv').config();

const initializeAdmin = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ role: 'admin' });
    
    if (existingAdmin) {
      console.log('Admin user already exists:', existingAdmin.email);
      return;
    }

    // Create admin user
    const adminData = {
      name: 'Cherif <PERSON>',
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      password: process.env.ADMIN_PASSWORD || 'admin123',
      role: 'admin'
    };

    const admin = await User.create(adminData);
    console.log('Admin user created successfully:', admin.email);

    // Initialize portfolio with default data
    const portfolio = await Portfolio.getPortfolio();
    console.log('Portfolio initialized successfully');

    // Add some default skills
    portfolio.skills.categories = [
      {
        name: 'Frontend Development',
        skills: [
          { name: 'React', level: 90, icon: 'react' },
          { name: 'JavaScript', level: 85, icon: 'javascript' },
          { name: 'TypeScript', level: 80, icon: 'typescript' },
          { name: 'HTML5', level: 95, icon: 'html5' },
          { name: 'CSS3', level: 90, icon: 'css3' },
          { name: 'Tailwind CSS', level: 85, icon: 'tailwindcss' }
        ]
      },
      {
        name: 'Backend Development',
        skills: [
          { name: 'Node.js', level: 80, icon: 'nodejs' },
          { name: 'Express.js', level: 75, icon: 'express' },
          { name: 'MongoDB', level: 70, icon: 'mongodb' },
          { name: 'REST APIs', level: 85, icon: 'api' }
        ]
      },
      {
        name: 'Tools & Technologies',
        skills: [
          { name: 'Git', level: 85, icon: 'git' },
          { name: 'VS Code', level: 90, icon: 'vscode' },
          { name: 'Figma', level: 70, icon: 'figma' },
          { name: 'Postman', level: 80, icon: 'postman' }
        ]
      }
    ];

    await portfolio.save();
    console.log('Default skills added to portfolio');

    console.log('\n✅ Initialization completed successfully!');
    console.log('\n📋 Admin Credentials:');
    console.log(`Email: ${adminData.email}`);
    console.log(`Password: ${adminData.password}`);
    console.log('\n⚠️  Please change the default password after first login!');

  } catch (error) {
    console.error('Initialization failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
    process.exit(0);
  }
};

// Run initialization if this file is executed directly
if (require.main === module) {
  initializeAdmin();
}

module.exports = initializeAdmin;
