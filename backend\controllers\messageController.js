const { Message } = require('../models');

// @desc    Create new contact message
// @route   POST /api/messages
// @access  Public
const createMessage = async (req, res) => {
  try {
    const messageData = {
      ...req.body,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      referrer: req.get('Referer')
    };

    const message = await Message.create(messageData);
    
    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: {
        id: message._id,
        status: message.status,
        createdAt: message.createdAt
      }
    });
  } catch (error) {
    console.error('Create message error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error sending message'
    });
  }
};

// @desc    Get all messages (admin)
// @route   GET /api/messages
// @access  Private/Admin
const getMessages = async (req, res) => {
  try {
    const {
      status,
      type,
      starred,
      spam,
      limit = 20,
      page = 1,
      sort = '-createdAt',
      search
    } = req.query;

    // Build query
    const query = {};

    if (status) query.status = status;
    if (type) query.type = type;
    if (starred !== undefined) query.isStarred = starred === 'true';
    if (spam !== undefined) query.isSpam = spam === 'true';

    // Add search functionality
    if (search) {
      const messages = await Message.search(search, {
        status,
        type,
        limit: parseInt(limit),
        skip: (page - 1) * limit
      });

      const total = await Message.countDocuments({
        ...query,
        $or: [
          { name: new RegExp(search, 'i') },
          { email: new RegExp(search, 'i') },
          { subject: new RegExp(search, 'i') },
          { message: new RegExp(search, 'i') },
          { company: new RegExp(search, 'i') }
        ]
      });

      return res.json({
        success: true,
        data: {
          messages,
          pagination: {
            current: parseInt(page),
            pages: Math.ceil(total / limit),
            total,
            limit: parseInt(limit)
          }
        }
      });
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute query
    const messages = await Message.find(query)
      .sort(sort)
      .limit(parseInt(limit))
      .skip(skip)
      .populate('response.sentBy', 'name email');

    // Get total count for pagination
    const total = await Message.countDocuments(query);

    res.json({
      success: true,
      data: {
        messages,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total,
          limit: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching messages'
    });
  }
};

// @desc    Get single message
// @route   GET /api/messages/:id
// @access  Private/Admin
const getMessage = async (req, res) => {
  try {
    const message = await Message.findById(req.params.id)
      .populate('response.sentBy', 'name email');

    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    // Mark as read if unread
    if (message.status === 'unread') {
      await message.markAsRead();
    }

    res.json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Get message error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching message'
    });
  }
};

// @desc    Update message status
// @route   PATCH /api/messages/:id/status
// @access  Private/Admin
const updateMessageStatus = async (req, res) => {
  try {
    const { status } = req.body;
    
    const message = await Message.findById(req.params.id);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    const oldStatus = message.status;
    message.status = status;

    // Set appropriate timestamps
    if (status === 'read' && oldStatus === 'unread') {
      message.readAt = new Date();
    } else if (status === 'archived') {
      message.archivedAt = new Date();
    }

    await message.save();
    
    res.json({
      success: true,
      message: 'Message status updated successfully',
      data: { status: message.status }
    });
  } catch (error) {
    console.error('Update message status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating message status'
    });
  }
};

// @desc    Toggle message star
// @route   PATCH /api/messages/:id/star
// @access  Private/Admin
const toggleStar = async (req, res) => {
  try {
    const message = await Message.findById(req.params.id);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    await message.toggleStar();
    
    res.json({
      success: true,
      message: `Message ${message.isStarred ? 'starred' : 'unstarred'} successfully`,
      data: { isStarred: message.isStarred }
    });
  } catch (error) {
    console.error('Toggle star error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error toggling star'
    });
  }
};

// @desc    Reply to message
// @route   POST /api/messages/:id/reply
// @access  Private/Admin
const replyToMessage = async (req, res) => {
  try {
    const { responseMessage } = req.body;
    
    const message = await Message.findById(req.params.id);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    await message.markAsReplied(responseMessage, req.user._id);
    
    res.json({
      success: true,
      message: 'Reply sent successfully',
      data: message.response
    });
  } catch (error) {
    console.error('Reply to message error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error sending reply'
    });
  }
};

// @desc    Mark message as spam
// @route   PATCH /api/messages/:id/spam
// @access  Private/Admin
const markAsSpam = async (req, res) => {
  try {
    const message = await Message.findById(req.params.id);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    await message.markAsSpam();
    
    res.json({
      success: true,
      message: 'Message marked as spam successfully'
    });
  } catch (error) {
    console.error('Mark as spam error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error marking message as spam'
    });
  }
};

// @desc    Delete message
// @route   DELETE /api/messages/:id
// @access  Private/Admin
const deleteMessage = async (req, res) => {
  try {
    const message = await Message.findById(req.params.id);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    await message.deleteOne();
    
    res.json({
      success: true,
      message: 'Message deleted successfully'
    });
  } catch (error) {
    console.error('Delete message error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error deleting message'
    });
  }
};

// @desc    Get message statistics
// @route   GET /api/messages/stats
// @access  Private/Admin
const getMessageStats = async (req, res) => {
  try {
    const [
      totalMessages,
      unreadCount,
      todayMessages,
      spamCount,
      starredCount
    ] = await Promise.all([
      Message.countDocuments({ isSpam: false }),
      Message.getUnreadCount(),
      Message.countDocuments({
        isSpam: false,
        createdAt: {
          $gte: new Date(new Date().setHours(0, 0, 0, 0))
        }
      }),
      Message.countDocuments({ isSpam: true }),
      Message.countDocuments({ isStarred: true, isSpam: false })
    ]);

    // Get message types distribution
    const typeStats = await Message.aggregate([
      { $match: { isSpam: false } },
      { $group: { _id: '$type', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Get recent activity (last 7 days)
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    
    const recentActivity = await Message.aggregate([
      {
        $match: {
          isSpam: false,
          createdAt: { $gte: weekAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.json({
      success: true,
      data: {
        totalMessages,
        unreadCount,
        todayMessages,
        spamCount,
        starredCount,
        typeStats,
        recentActivity
      }
    });
  } catch (error) {
    console.error('Get message stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching message statistics'
    });
  }
};

module.exports = {
  createMessage,
  getMessages,
  getMessage,
  updateMessageStatus,
  toggleStar,
  replyToMessage,
  markAsSpam,
  deleteMessage,
  getMessageStats
};
