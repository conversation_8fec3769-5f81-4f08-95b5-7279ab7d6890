import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Filter, 
  Mail, 
  MailOpen,
  Star,
  Trash2,
  Reply,
  Archive,
  AlertTriangle,
  Calendar,
  User,
  Building,
  Phone
} from 'lucide-react';
import AdminLayout from '../../components/admin/AdminLayout';
import { usePortfolio } from '../../contexts/PortfolioContext';
import LoadingSpinner from '../../components/LoadingSpinner';
import MessageModal from '../../components/admin/MessageModal';
import axios from 'axios';

const Messages = () => {
  const { fetchMessages } = usePortfolio();
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [filteredMessages, setFilteredMessages] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    unread: 0,
    starred: 0,
    spam: 0
  });

  const statuses = [
    { value: 'all', label: 'All Messages' },
    { value: 'unread', label: 'Unread' },
    { value: 'read', label: 'Read' },
    { value: 'replied', label: 'Replied' },
    { value: 'archived', label: 'Archived' }
  ];

  const types = [
    { value: 'all', label: 'All Types' },
    { value: 'general', label: 'General' },
    { value: 'project-inquiry', label: 'Project Inquiry' },
    { value: 'job-opportunity', label: 'Job Opportunity' },
    { value: 'collaboration', label: 'Collaboration' },
    { value: 'feedback', label: 'Feedback' },
    { value: 'other', label: 'Other' }
  ];

  useEffect(() => {
    loadMessages();
    loadStats();
  }, []);

  useEffect(() => {
    filterMessages();
  }, [messages, searchTerm, selectedStatus, selectedType]);

  const loadMessages = async () => {
    setLoading(true);
    const result = await fetchMessages();
    if (result?.messages) {
      setMessages(result.messages);
    }
    setLoading(false);
  };

  const loadStats = async () => {
    try {
      const response = await axios.get('/messages/stats');
      if (response.data.success) {
        setStats({
          total: response.data.data.totalMessages,
          unread: response.data.data.unreadCount,
          starred: response.data.data.starredCount,
          spam: response.data.data.spamCount
        });
      }
    } catch (error) {
      console.error('Failed to load message stats:', error);
    }
  };

  const filterMessages = () => {
    let filtered = messages;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(message =>
        message.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (message.company && message.company.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(message => message.status === selectedStatus);
    }

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter(message => message.type === selectedType);
    }

    setFilteredMessages(filtered);
  };

  const handleMessageClick = (message) => {
    setSelectedMessage(message);
    setShowModal(true);
    
    // Mark as read if unread
    if (message.status === 'unread') {
      handleStatusChange(message._id, 'read');
    }
  };

  const handleStatusChange = async (messageId, newStatus) => {
    try {
      const response = await axios.patch(`/messages/${messageId}/status`, {
        status: newStatus
      });
      
      if (response.data.success) {
        setMessages(messages.map(msg => 
          msg._id === messageId ? { ...msg, status: newStatus } : msg
        ));
        
        // Update stats
        if (newStatus === 'read') {
          setStats(prev => ({ ...prev, unread: Math.max(0, prev.unread - 1) }));
        }
      }
    } catch (error) {
      console.error('Failed to update message status:', error);
    }
  };

  const handleToggleStar = async (messageId) => {
    try {
      const response = await axios.patch(`/messages/${messageId}/star`);
      
      if (response.data.success) {
        setMessages(messages.map(msg => 
          msg._id === messageId ? { ...msg, isStarred: response.data.data.isStarred } : msg
        ));
      }
    } catch (error) {
      console.error('Failed to toggle star:', error);
    }
  };

  const handleDelete = async (messageId) => {
    if (window.confirm('Are you sure you want to delete this message?')) {
      try {
        const response = await axios.delete(`/messages/${messageId}`);
        
        if (response.data.success) {
          setMessages(messages.filter(msg => msg._id !== messageId));
          setStats(prev => ({ ...prev, total: prev.total - 1 }));
        }
      } catch (error) {
        console.error('Failed to delete message:', error);
      }
    }
  };

  const handleMarkAsSpam = async (messageId) => {
    try {
      const response = await axios.patch(`/messages/${messageId}/spam`);
      
      if (response.data.success) {
        setMessages(messages.filter(msg => msg._id !== messageId));
        setStats(prev => ({ 
          ...prev, 
          total: prev.total - 1,
          spam: prev.spam + 1 
        }));
      }
    } catch (error) {
      console.error('Failed to mark as spam:', error);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      unread: 'bg-neon-blue/20 text-neon-blue',
      read: 'bg-gray-500/20 text-gray-400',
      replied: 'bg-green-500/20 text-green-400',
      archived: 'bg-yellow-500/20 text-yellow-400'
    };
    return colors[status] || 'bg-gray-500/20 text-gray-400';
  };

  const getTypeColor = (type) => {
    const colors = {
      'project-inquiry': 'bg-neon-purple/20 text-neon-purple',
      'job-opportunity': 'bg-neon-green/20 text-neon-green',
      'collaboration': 'bg-neon-pink/20 text-neon-pink',
      'feedback': 'bg-yellow-500/20 text-yellow-400',
      'general': 'bg-blue-500/20 text-blue-400',
      'other': 'bg-gray-500/20 text-gray-400'
    };
    return colors[type] || 'bg-gray-500/20 text-gray-400';
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  };

  return (
    <AdminLayout title="Messages">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >
        {/* Header */}
        <motion.div variants={itemVariants} className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold text-white">Messages</h2>
            <p className="text-gray-400">Manage contact form submissions</p>
          </div>
        </motion.div>

        {/* Stats */}
        <motion.div variants={itemVariants} className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { label: 'Total', value: stats.total, icon: Mail, color: 'from-neon-blue to-blue-600' },
            { label: 'Unread', value: stats.unread, icon: MailOpen, color: 'from-neon-purple to-purple-600' },
            { label: 'Starred', value: stats.starred, icon: Star, color: 'from-neon-green to-green-600' },
            { label: 'Spam', value: stats.spam, icon: AlertTriangle, color: 'from-red-500 to-red-600' }
          ].map((stat) => {
            const Icon = stat.icon;
            return (
              <div key={stat.label} className="card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold text-white">{stat.value}</p>
                    <p className="text-sm text-gray-400">{stat.label}</p>
                  </div>
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${stat.color}`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                </div>
              </div>
            );
          })}
        </motion.div>

        {/* Filters */}
        <motion.div variants={itemVariants} className="card">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search messages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 glass rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neon-blue/50"
              />
            </div>

            {/* Status Filter */}
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="input"
            >
              {statuses.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>

            {/* Type Filter */}
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="input"
            >
              {types.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>

            {/* Results Count */}
            <div className="flex items-center text-gray-400">
              <Filter className="w-4 h-4 mr-2" />
              <span className="text-sm">
                {filteredMessages.length} of {messages.length} messages
              </span>
            </div>
          </div>
        </motion.div>

        {/* Messages List */}
        {loading ? (
          <div className="flex justify-center py-20">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <motion.div variants={containerVariants} className="space-y-4">
            <AnimatePresence>
              {filteredMessages.map((message) => (
                <motion.div
                  key={message._id}
                  variants={itemVariants}
                  layout
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  onClick={() => handleMessageClick(message)}
                  className={`card-hover cursor-pointer ${
                    message.status === 'unread' ? 'border-l-4 border-neon-blue' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      {/* Avatar */}
                      <div className="w-12 h-12 bg-gradient-to-r from-neon-blue to-neon-purple rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white font-medium">
                          {message.name.charAt(0).toUpperCase()}
                        </span>
                      </div>

                      {/* Message Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            <h3 className={`font-medium ${
                              message.status === 'unread' ? 'text-white' : 'text-gray-300'
                            }`}>
                              {message.name}
                            </h3>
                            {message.company && (
                              <span className="flex items-center text-sm text-gray-400">
                                <Building className="w-3 h-3 mr-1" />
                                {message.company}
                              </span>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(message.status)}`}>
                              {message.status}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor(message.type)}`}>
                              {message.type.replace('-', ' ')}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4 text-sm text-gray-400 mb-2">
                          <span className="flex items-center">
                            <Mail className="w-3 h-3 mr-1" />
                            {message.email}
                          </span>
                          {message.phone && (
                            <span className="flex items-center">
                              <Phone className="w-3 h-3 mr-1" />
                              {message.phone}
                            </span>
                          )}
                          <span className="flex items-center">
                            <Calendar className="w-3 h-3 mr-1" />
                            {new Date(message.createdAt).toLocaleDateString()}
                          </span>
                        </div>

                        <h4 className={`font-medium mb-1 ${
                          message.status === 'unread' ? 'text-white' : 'text-gray-300'
                        }`}>
                          {message.subject}
                        </h4>
                        
                        <p className="text-gray-400 text-sm line-clamp-2">
                          {message.message}
                        </p>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      {message.isStarred && (
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      )}
                      
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleStar(message._id);
                        }}
                        className="p-2 glass rounded-lg hover:bg-yellow-500/20 transition-colors opacity-0 group-hover:opacity-100"
                      >
                        <Star className={`w-4 h-4 ${message.isStarred ? 'text-yellow-400 fill-current' : 'text-gray-400'}`} />
                      </button>

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMarkAsSpam(message._id);
                        }}
                        className="p-2 glass rounded-lg hover:bg-red-500/20 transition-colors opacity-0 group-hover:opacity-100"
                      >
                        <AlertTriangle className="w-4 h-4 text-gray-400 hover:text-red-400" />
                      </button>

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(message._id);
                        }}
                        className="p-2 glass rounded-lg hover:bg-red-500/20 transition-colors opacity-0 group-hover:opacity-100"
                      >
                        <Trash2 className="w-4 h-4 text-gray-400 hover:text-red-400" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>
        )}

        {/* Empty State */}
        {!loading && filteredMessages.length === 0 && (
          <motion.div
            variants={itemVariants}
            className="text-center py-20"
          >
            <Mail size={64} className="text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-400 mb-2">
              No messages found
            </h3>
            <p className="text-gray-500">
              {messages.length === 0 
                ? "No messages have been received yet"
                : "Try adjusting your filters or search term"
              }
            </p>
          </motion.div>
        )}
      </motion.div>

      {/* Message Modal */}
      <MessageModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        message={selectedMessage}
        onStatusChange={handleStatusChange}
        onToggleStar={handleToggleStar}
        onDelete={handleDelete}
        onMarkAsSpam={handleMarkAsSpam}
      />
    </AdminLayout>
  );
};

export default Messages;
