# Server Configuration
PORT=5000
NODE_ENV=development

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/portfolio?retryWrites=true&w=majority

# JWT Authentication
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRE=7d

# GitHub Integration
GITHUB_TOKEN=your_github_personal_access_token
GITHUB_USERNAME=chxb07

# Email Configuration (Optional - for contact form notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Admin User (Default admin credentials)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# CORS Configuration
FRONTEND_URL=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
