@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-950 text-white font-sans antialiased;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Glassmorphism effect */
  .glass {
    @apply backdrop-blur-md bg-white/5 border border-white/10;
  }

  .glass-strong {
    @apply backdrop-blur-lg bg-white/10 border border-white/20;
  }

  /* Neon glow effects */
  .glow-blue {
    box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
  }

  .glow-purple {
    box-shadow: 0 0 20px rgba(191, 0, 255, 0.3);
  }

  .glow-pink {
    box-shadow: 0 0 20px rgba(255, 0, 128, 0.3);
  }

  .glow-green {
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
  }

  /* Animated gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-neon-blue via-neon-purple to-neon-pink bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  /* Animated gradient background */
  .gradient-bg {
    background: linear-gradient(-45deg, #0ea5e9, #8b5cf6, #ec4899, #06b6d4);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-900/50;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-neon-blue/50 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-neon-blue/70;
  }

  /* Button styles */
  .btn-primary {
    @apply px-6 py-3 bg-gradient-to-r from-neon-blue to-neon-purple text-white font-medium rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-neon-blue/25 hover:scale-105;
  }

  .btn-secondary {
    @apply px-6 py-3 glass text-white font-medium rounded-lg transition-all duration-300 hover:bg-white/10 hover:border-white/30;
  }

  .btn-ghost {
    @apply px-4 py-2 text-gray-300 hover:text-white transition-colors duration-300;
  }

  /* Card styles */
  .card {
    @apply glass rounded-xl p-6 transition-all duration-300 hover:bg-white/10 hover:border-white/30;
  }

  .card-hover {
    @apply card hover:transform hover:scale-105 hover:shadow-xl hover:shadow-neon-blue/10;
  }

  /* Input styles */
  .input {
    @apply w-full px-4 py-3 glass rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neon-blue/50 focus:border-transparent transition-all duration-300;
  }

  .textarea {
    @apply input resize-none;
  }

  /* Navigation styles */
  .nav-link {
    @apply text-gray-300 hover:text-white transition-colors duration-300 relative;
  }

  .nav-link.active {
    @apply text-neon-blue;
  }

  .nav-link::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-neon-blue transition-all duration-300;
  }

  .nav-link:hover::after,
  .nav-link.active::after {
    @apply w-full;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.7);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }
  50% {
    box-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}
