import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FolderOpen, 
  MessageSquare, 
  Eye, 
  TrendingUp,
  Users,
  Star,
  Calendar,
  Activity
} from 'lucide-react';
import AdminLayout from '../../components/admin/AdminLayout';
import { usePortfolio } from '../../contexts/PortfolioContext';
import LoadingSpinner from '../../components/LoadingSpinner';
import axios from 'axios';

const Dashboard = () => {
  const { portfolio, featuredProjects, loading } = usePortfolio();
  const [stats, setStats] = useState({
    totalProjects: 0,
    featuredProjects: 0,
    totalMessages: 0,
    unreadMessages: 0,
    portfolioViews: 0,
    projectClicks: 0
  });
  const [recentMessages, setRecentMessages] = useState([]);
  const [statsLoading, setStatsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setStatsLoading(true);
      
      // Fetch projects stats
      const projectsResponse = await axios.get('/projects/admin/all?limit=1');
      
      // Fetch messages stats
      const messagesStatsResponse = await axios.get('/messages/stats');
      
      // Fetch recent messages
      const recentMessagesResponse = await axios.get('/messages?limit=5&sort=-createdAt');
      
      // Fetch portfolio stats
      const portfolioStatsResponse = await axios.get('/portfolio/stats');

      setStats({
        totalProjects: projectsResponse.data.data.pagination.total,
        featuredProjects: featuredProjects?.length || 0,
        totalMessages: messagesStatsResponse.data.data.totalMessages,
        unreadMessages: messagesStatsResponse.data.data.unreadCount,
        portfolioViews: Math.floor(Math.random() * 1000) + 500, // Mock data
        projectClicks: Math.floor(Math.random() * 500) + 200 // Mock data
      });

      setRecentMessages(recentMessagesResponse.data.data.messages || []);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  const statCards = [
    {
      title: 'Total Projects',
      value: stats.totalProjects,
      icon: FolderOpen,
      color: 'from-neon-blue to-blue-600',
      change: '+2 this month'
    },
    {
      title: 'Featured Projects',
      value: stats.featuredProjects,
      icon: Star,
      color: 'from-neon-purple to-purple-600',
      change: `${Math.round((stats.featuredProjects / stats.totalProjects) * 100) || 0}% of total`
    },
    {
      title: 'Total Messages',
      value: stats.totalMessages,
      icon: MessageSquare,
      color: 'from-neon-green to-green-600',
      change: `${stats.unreadMessages} unread`
    },
    {
      title: 'Portfolio Views',
      value: stats.portfolioViews,
      icon: Eye,
      color: 'from-neon-pink to-pink-600',
      change: '+12% this week'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  };

  if (loading || statsLoading) {
    return (
      <AdminLayout title="Dashboard">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Dashboard">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >
        {/* Welcome Section */}
        <motion.div variants={itemVariants} className="card">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-white mb-2">
                Welcome back! 👋
              </h2>
              <p className="text-gray-400">
                Here's what's happening with your portfolio today.
              </p>
            </div>
            <div className="hidden md:block">
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <Calendar className="w-4 h-4" />
                <span>{new Date().toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.title}
                variants={itemVariants}
                whileHover={{ scale: 1.02 }}
                className="card-hover"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${stat.color}`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <TrendingUp className="w-5 h-5 text-green-400" />
                </div>
                
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">
                    {stat.value.toLocaleString()}
                  </h3>
                  <p className="text-sm text-gray-400 mb-2">{stat.title}</p>
                  <p className="text-xs text-green-400">{stat.change}</p>
                </div>
              </motion.div>
            );
          })}
        </div>

        <div className="grid lg:grid-cols-2 gap-6">
          {/* Recent Messages */}
          <motion.div variants={itemVariants} className="card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white">Recent Messages</h3>
              <MessageSquare className="w-5 h-5 text-neon-blue" />
            </div>

            <div className="space-y-4">
              {recentMessages.length > 0 ? (
                recentMessages.map((message) => (
                  <div
                    key={message._id}
                    className="flex items-start space-x-3 p-3 glass rounded-lg hover:bg-white/10 transition-colors"
                  >
                    <div className="w-8 h-8 bg-gradient-to-r from-neon-blue to-neon-purple rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-sm font-medium">
                        {message.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-white truncate">
                          {message.name}
                        </p>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          message.status === 'unread' 
                            ? 'bg-neon-blue/20 text-neon-blue' 
                            : 'bg-gray-700 text-gray-300'
                        }`}>
                          {message.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-400 truncate">
                        {message.subject}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(message.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-400">
                  <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No messages yet</p>
                </div>
              )}
            </div>
          </motion.div>

          {/* Portfolio Status */}
          <motion.div variants={itemVariants} className="card">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white">Portfolio Status</h3>
              <Activity className="w-5 h-5 text-neon-green" />
            </div>

            <div className="space-y-4">
              {/* Section Visibility */}
              <div>
                <h4 className="text-sm font-medium text-gray-300 mb-3">Section Visibility</h4>
                <div className="space-y-2">
                  {portfolio?.sectionVisibility && Object.entries(portfolio.sectionVisibility).map(([section, isVisible]) => (
                    <div key={section} className="flex items-center justify-between">
                      <span className="text-sm text-gray-400 capitalize">
                        {section.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                      <div className={`w-3 h-3 rounded-full ${
                        isVisible ? 'bg-green-400' : 'bg-gray-600'
                      }`} />
                    </div>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="pt-4 border-t border-white/10">
                <h4 className="text-sm font-medium text-gray-300 mb-3">Quick Actions</h4>
                <div className="space-y-2">
                  <button className="w-full text-left px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors">
                    Update About Section
                  </button>
                  <button className="w-full text-left px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors">
                    Add New Project
                  </button>
                  <button className="w-full text-left px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors">
                    Review Messages
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Activity Feed */}
        <motion.div variants={itemVariants} className="card">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">Recent Activity</h3>
            <Activity className="w-5 h-5 text-neon-purple" />
          </div>

          <div className="space-y-4">
            {[
              { action: 'New message received', time: '2 hours ago', type: 'message' },
              { action: 'Project "Portfolio Website" updated', time: '1 day ago', type: 'project' },
              { action: 'Skills section modified', time: '2 days ago', type: 'portfolio' },
              { action: 'New project "E-commerce App" added', time: '3 days ago', type: 'project' }
            ].map((activity, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 glass rounded-lg">
                <div className={`w-2 h-2 rounded-full ${
                  activity.type === 'message' ? 'bg-neon-blue' :
                  activity.type === 'project' ? 'bg-neon-purple' : 'bg-neon-green'
                }`} />
                <div className="flex-1">
                  <p className="text-sm text-white">{activity.action}</p>
                  <p className="text-xs text-gray-400">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </motion.div>
    </AdminLayout>
  );
};

export default Dashboard;
