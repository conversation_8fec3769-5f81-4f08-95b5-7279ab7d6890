import { motion } from 'framer-motion';
import { User, Code, Coffee, Heart } from 'lucide-react';

const About = ({ data }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  };

  const stats = [
    { icon: Code, label: 'Projects Completed', value: '15+' },
    { icon: Coffee, label: 'Cups of Coffee', value: '∞' },
    { icon: Heart, label: 'Years of Passion', value: '3+' },
    { icon: User, label: 'Happy Clients', value: '10+' }
  ];

  return (
    <section id="about" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-neon-purple/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-1/4 w-72 h-72 bg-neon-blue/5 rounded-full blur-3xl" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">
                {data?.title || 'About Me'}
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-neon-blue to-neon-purple mx-auto rounded-full" />
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Profile Image */}
            <motion.div variants={itemVariants} className="relative">
              <div className="relative w-full max-w-md mx-auto">
                {/* Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-neon-blue to-neon-purple rounded-2xl blur-xl opacity-20" />
                
                {/* Image Container */}
                <div className="relative glass rounded-2xl p-8 overflow-hidden">
                  {data?.profileImage ? (
                    <img
                      src={data.profileImage}
                      alt={data.name}
                      className="w-full h-auto rounded-xl"
                    />
                  ) : (
                    <div className="w-full h-80 bg-gradient-to-br from-neon-blue/20 to-neon-purple/20 rounded-xl flex items-center justify-center">
                      <User size={80} className="text-neon-blue/50" />
                    </div>
                  )}
                  
                  {/* Floating Elements */}
                  <motion.div
                    animate={{
                      y: [0, -10, 0],
                      rotate: [0, 5, 0]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }}
                    className="absolute -top-4 -right-4 w-8 h-8 bg-neon-pink rounded-full opacity-60"
                  />
                  <motion.div
                    animate={{
                      y: [0, 10, 0],
                      rotate: [0, -5, 0]
                    }}
                    transition={{
                      duration: 5,
                      repeat: Infinity,
                      ease: 'easeInOut',
                      delay: 1
                    }}
                    className="absolute -bottom-4 -left-4 w-6 h-6 bg-neon-green rounded-full opacity-40"
                  />
                </div>
              </div>
            </motion.div>

            {/* Content */}
            <motion.div variants={itemVariants} className="space-y-8">
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-white mb-6">
                  {data?.name || 'Cherif Bourechache'}
                </h3>
                <p className="text-lg text-gray-300 leading-relaxed mb-6">
                  {data?.description || 
                    'Passionate about creating modern, responsive web applications with cutting-edge technologies. Currently pursuing Computer Science while building innovative projects and expanding my expertise in frontend development.'
                  }
                </p>
                <p className="text-gray-400 leading-relaxed">
                  I believe in writing clean, maintainable code and creating user experiences that are both beautiful and functional. When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or enjoying a good cup of coffee.
                </p>
              </div>

              {/* Skills Highlight */}
              <div className="space-y-4">
                <h4 className="text-xl font-semibold text-white">What I Do</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {[
                    'Frontend Development',
                    'UI/UX Design',
                    'Responsive Design',
                    'API Integration'
                  ].map((skill, index) => (
                    <motion.div
                      key={skill}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center space-x-3"
                    >
                      <div className="w-2 h-2 bg-neon-blue rounded-full" />
                      <span className="text-gray-300">{skill}</span>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* CTA Button */}
              {data?.resumeUrl && (
                <motion.a
                  href={data.resumeUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="btn-primary inline-flex items-center"
                >
                  Download Resume
                </motion.a>
              )}
            </motion.div>
          </div>

          {/* Stats Section */}
          <motion.div
            variants={itemVariants}
            className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="text-center group"
                >
                  <div className="glass rounded-xl p-6 hover:bg-white/10 transition-all duration-300 group-hover:scale-105">
                    <Icon className="w-8 h-8 text-neon-blue mx-auto mb-4 group-hover:text-neon-purple transition-colors" />
                    <div className="text-2xl md:text-3xl font-bold text-white mb-2">
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-400">
                      {stat.label}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
