const express = require('express');
const {
  getPortfolio,
  getPortfolioAdmin,
  updateAbout,
  updateSkills,
  updateContact,
  updateSectionVisibility,
  updateSEO,
  updateTheme,
  getPortfolioStats
} = require('../controllers/portfolioController');
const { authenticateToken, requireAdmin, optionalAuth } = require('../middleware/auth');
const {
  validatePortfolioAbout,
  validatePortfolioContact,
  validateSkills
} = require('../middleware/validation');

const router = express.Router();

// @route   GET /api/portfolio
// @desc    Get portfolio data (public)
// @access  Public
router.get('/', optionalAuth, getPortfolio);

// @route   GET /api/portfolio/admin
// @desc    Get full portfolio data (admin)
// @access  Private/Admin
router.get('/admin', authenticateToken, requireAdmin, getPortfolioAdmin);

// @route   PUT /api/portfolio/about
// @desc    Update about section
// @access  Private/Admin
router.put('/about', authenticateToken, requireAdmin, validatePortfolioAbout, updateAbout);

// @route   PUT /api/portfolio/skills
// @desc    Update skills section
// @access  Private/Admin
router.put('/skills', authenticateToken, requireAdmin, validateSkills, updateSkills);

// @route   PUT /api/portfolio/contact
// @desc    Update contact section
// @access  Private/Admin
router.put('/contact', authenticateToken, requireAdmin, validatePortfolioContact, updateContact);

// @route   PUT /api/portfolio/visibility
// @desc    Update section visibility
// @access  Private/Admin
router.put('/visibility', authenticateToken, requireAdmin, updateSectionVisibility);

// @route   PUT /api/portfolio/seo
// @desc    Update SEO settings
// @access  Private/Admin
router.put('/seo', authenticateToken, requireAdmin, updateSEO);

// @route   PUT /api/portfolio/theme
// @desc    Update theme settings
// @access  Private/Admin
router.put('/theme', authenticateToken, requireAdmin, updateTheme);

// @route   GET /api/portfolio/stats
// @desc    Get portfolio statistics
// @access  Private/Admin
router.get('/stats', authenticateToken, requireAdmin, getPortfolioStats);

module.exports = router;
