import { useEffect } from 'react';
import { motion } from 'framer-motion';
import { usePortfolio } from '../contexts/PortfolioContext';

// Components
import Navbar from '../components/Navbar';
import Hero from '../components/sections/Hero';
import About from '../components/sections/About';
import Skills from '../components/sections/Skills';
import Projects from '../components/sections/Projects';
import Contact from '../components/sections/Contact';
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';

const Portfolio = () => {
  const { portfolio, loading, error } = usePortfolio();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-400 mb-4">Error Loading Portfolio</h1>
          <p className="text-gray-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-gray-950"
    >
      <Navbar />
      
      <main>
        {/* Hero Section */}
        <Hero />
        
        {/* About Section */}
        {portfolio?.sectionVisibility?.about && (
          <About data={portfolio.about} />
        )}
        
        {/* Skills Section */}
        {portfolio?.sectionVisibility?.skills && (
          <Skills data={portfolio.skills} />
        )}
        
        {/* Projects Section */}
        {portfolio?.sectionVisibility?.projects && (
          <Projects />
        )}
        
        {/* Contact Section */}
        {portfolio?.sectionVisibility?.contact && (
          <Contact data={portfolio.contact} />
        )}
      </main>
      
      <Footer />
    </motion.div>
  );
};

export default Portfolio;
