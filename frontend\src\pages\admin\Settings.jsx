import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Save, 
  User, 
  Mail, 
  Globe, 
  Palette, 
  Eye, 
  EyeOff,
  Lock,
  Settings as SettingsIcon,
  Info
} from 'lucide-react';
import AdminLayout from '../../components/admin/AdminLayout';
import { usePortfolio } from '../../contexts/PortfolioContext';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/LoadingSpinner';

const Settings = () => {
  const { portfolio, updatePortfolioSection, loading } = usePortfolio();
  const { user, updateProfile, changePassword } = useAuth();
  
  const [activeTab, setActiveTab] = useState('about');
  const [saving, setSaving] = useState(false);
  
  // About section state
  const [aboutData, setAboutData] = useState({
    title: '',
    name: '',
    tagline: '',
    description: '',
    profileImage: '',
    resumeUrl: ''
  });

  // Contact section state
  const [contactData, setContactData] = useState({
    title: '',
    email: '',
    phone: '',
    location: '',
    socialLinks: {
      github: '',
      linkedin: '',
      twitter: '',
      website: ''
    }
  });

  // SEO section state
  const [seoData, setSeoData] = useState({
    title: '',
    description: '',
    keywords: []
  });

  // Theme section state
  const [themeData, setThemeData] = useState({
    primaryColor: '#0ea5e9',
    accentColor: '#00f5ff',
    darkMode: true
  });

  // Section visibility state
  const [visibilityData, setVisibilityData] = useState({
    hero: true,
    about: true,
    skills: true,
    projects: true,
    contact: true
  });

  // Profile update state
  const [profileData, setProfileData] = useState({
    name: '',
    email: ''
  });

  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [newKeyword, setNewKeyword] = useState('');

  const tabs = [
    { id: 'about', label: 'About Me', icon: User },
    { id: 'contact', label: 'Contact Info', icon: Mail },
    { id: 'seo', label: 'SEO Settings', icon: Globe },
    { id: 'theme', label: 'Theme', icon: Palette },
    { id: 'visibility', label: 'Visibility', icon: Eye },
    { id: 'profile', label: 'Profile', icon: SettingsIcon },
    { id: 'security', label: 'Security', icon: Lock }
  ];

  useEffect(() => {
    if (portfolio) {
      setAboutData(portfolio.about || {});
      setContactData(portfolio.contact || {});
      setSeoData(portfolio.seo || {});
      setThemeData(portfolio.theme || {});
      setVisibilityData(portfolio.sectionVisibility || {});
    }
  }, [portfolio]);

  useEffect(() => {
    if (user) {
      setProfileData({
        name: user.name || '',
        email: user.email || ''
      });
    }
  }, [user]);

  const handleSave = async (section, data) => {
    setSaving(true);
    await updatePortfolioSection(section, data);
    setSaving(false);
  };

  const handleProfileUpdate = async () => {
    setSaving(true);
    await updateProfile(profileData);
    setSaving(false);
  };

  const handlePasswordChange = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('New passwords do not match');
      return;
    }

    setSaving(true);
    const result = await changePassword(passwordData.currentPassword, passwordData.newPassword);
    if (result.success) {
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    }
    setSaving(false);
  };

  const handleAddKeyword = () => {
    if (newKeyword.trim() && !seoData.keywords.includes(newKeyword.trim())) {
      setSeoData(prev => ({
        ...prev,
        keywords: [...prev.keywords, newKeyword.trim()]
      }));
      setNewKeyword('');
    }
  };

  const handleRemoveKeyword = (index) => {
    setSeoData(prev => ({
      ...prev,
      keywords: prev.keywords.filter((_, i) => i !== index)
    }));
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  };

  if (loading) {
    return (
      <AdminLayout title="Settings">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Settings">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >
        {/* Header */}
        <motion.div variants={itemVariants}>
          <h2 className="text-2xl font-bold text-white mb-2">Settings</h2>
          <p className="text-gray-400">Manage your portfolio configuration</p>
        </motion.div>

        <div className="grid lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <motion.div variants={itemVariants} className="lg:col-span-1">
            <div className="card space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-4 py-3 rounded-lg transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-neon-blue/20 text-neon-blue border border-neon-blue/30'
                        : 'text-gray-300 hover:bg-white/10 hover:text-white'
                    }`}
                  >
                    <Icon className="w-5 h-5 mr-3" />
                    {tab.label}
                  </button>
                );
              })}
            </div>
          </motion.div>

          {/* Content */}
          <motion.div variants={itemVariants} className="lg:col-span-3">
            <div className="card">
              {/* About Me Tab */}
              {activeTab === 'about' && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-white">About Me Section</h3>
                  
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Section Title
                      </label>
                      <input
                        type="text"
                        value={aboutData.title || ''}
                        onChange={(e) => setAboutData(prev => ({ ...prev, title: e.target.value }))}
                        className="input"
                        placeholder="About Me"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Your Name
                      </label>
                      <input
                        type="text"
                        value={aboutData.name || ''}
                        onChange={(e) => setAboutData(prev => ({ ...prev, name: e.target.value }))}
                        className="input"
                        placeholder="Your full name"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Tagline
                    </label>
                    <input
                      type="text"
                      value={aboutData.tagline || ''}
                      onChange={(e) => setAboutData(prev => ({ ...prev, tagline: e.target.value }))}
                      className="input"
                      placeholder="Your professional tagline"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Description
                    </label>
                    <textarea
                      value={aboutData.description || ''}
                      onChange={(e) => setAboutData(prev => ({ ...prev, description: e.target.value }))}
                      rows={4}
                      className="textarea"
                      placeholder="Tell visitors about yourself..."
                    />
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Profile Image URL
                      </label>
                      <input
                        type="url"
                        value={aboutData.profileImage || ''}
                        onChange={(e) => setAboutData(prev => ({ ...prev, profileImage: e.target.value }))}
                        className="input"
                        placeholder="https://example.com/profile.jpg"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Resume URL
                      </label>
                      <input
                        type="url"
                        value={aboutData.resumeUrl || ''}
                        onChange={(e) => setAboutData(prev => ({ ...prev, resumeUrl: e.target.value }))}
                        className="input"
                        placeholder="https://example.com/resume.pdf"
                      />
                    </div>
                  </div>

                  <button
                    onClick={() => handleSave('about', aboutData)}
                    disabled={saving}
                    className="btn-primary disabled:opacity-50"
                  >
                    {saving ? <LoadingSpinner size="sm" className="mr-2" /> : <Save className="w-4 h-4 mr-2" />}
                    Save About Section
                  </button>
                </div>
              )}

              {/* Contact Tab */}
              {activeTab === 'contact' && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-white">Contact Information</h3>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Section Title
                    </label>
                    <input
                      type="text"
                      value={contactData.title || ''}
                      onChange={(e) => setContactData(prev => ({ ...prev, title: e.target.value }))}
                      className="input"
                      placeholder="Get In Touch"
                    />
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={contactData.email || ''}
                        onChange={(e) => setContactData(prev => ({ ...prev, email: e.target.value }))}
                        className="input"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={contactData.phone || ''}
                        onChange={(e) => setContactData(prev => ({ ...prev, phone: e.target.value }))}
                        className="input"
                        placeholder="+****************"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Location
                    </label>
                    <input
                      type="text"
                      value={contactData.location || ''}
                      onChange={(e) => setContactData(prev => ({ ...prev, location: e.target.value }))}
                      className="input"
                      placeholder="City, Country"
                    />
                  </div>

                  <div>
                    <h4 className="text-lg font-medium text-white mb-4">Social Links</h4>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          GitHub URL
                        </label>
                        <input
                          type="url"
                          value={contactData.socialLinks?.github || ''}
                          onChange={(e) => setContactData(prev => ({
                            ...prev,
                            socialLinks: { ...prev.socialLinks, github: e.target.value }
                          }))}
                          className="input"
                          placeholder="https://github.com/username"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          LinkedIn URL
                        </label>
                        <input
                          type="url"
                          value={contactData.socialLinks?.linkedin || ''}
                          onChange={(e) => setContactData(prev => ({
                            ...prev,
                            socialLinks: { ...prev.socialLinks, linkedin: e.target.value }
                          }))}
                          className="input"
                          placeholder="https://linkedin.com/in/username"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Twitter URL
                        </label>
                        <input
                          type="url"
                          value={contactData.socialLinks?.twitter || ''}
                          onChange={(e) => setContactData(prev => ({
                            ...prev,
                            socialLinks: { ...prev.socialLinks, twitter: e.target.value }
                          }))}
                          className="input"
                          placeholder="https://twitter.com/username"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Website URL
                        </label>
                        <input
                          type="url"
                          value={contactData.socialLinks?.website || ''}
                          onChange={(e) => setContactData(prev => ({
                            ...prev,
                            socialLinks: { ...prev.socialLinks, website: e.target.value }
                          }))}
                          className="input"
                          placeholder="https://yourwebsite.com"
                        />
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={() => handleSave('contact', contactData)}
                    disabled={saving}
                    className="btn-primary disabled:opacity-50"
                  >
                    {saving ? <LoadingSpinner size="sm" className="mr-2" /> : <Save className="w-4 h-4 mr-2" />}
                    Save Contact Info
                  </button>
                </div>
              )}

              {/* SEO Tab */}
              {activeTab === 'seo' && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-white">SEO Settings</h3>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Page Title
                    </label>
                    <input
                      type="text"
                      value={seoData.title || ''}
                      onChange={(e) => setSeoData(prev => ({ ...prev, title: e.target.value }))}
                      className="input"
                      placeholder="Your Name - Portfolio"
                      maxLength={60}
                    />
                    <p className="text-xs text-gray-400 mt-1">
                      {(seoData.title || '').length}/60 characters
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Meta Description
                    </label>
                    <textarea
                      value={seoData.description || ''}
                      onChange={(e) => setSeoData(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className="textarea"
                      placeholder="Brief description of your portfolio..."
                      maxLength={160}
                    />
                    <p className="text-xs text-gray-400 mt-1">
                      {(seoData.description || '').length}/160 characters
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Keywords
                    </label>
                    <div className="flex gap-2 mb-4">
                      <input
                        type="text"
                        value={newKeyword}
                        onChange={(e) => setNewKeyword(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleAddKeyword()}
                        className="flex-1 input"
                        placeholder="Add keyword"
                      />
                      <button
                        onClick={handleAddKeyword}
                        className="btn-secondary px-4"
                      >
                        Add
                      </button>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      {(seoData.keywords || []).map((keyword, index) => (
                        <span
                          key={index}
                          className="flex items-center px-3 py-1 bg-neon-blue/20 text-neon-blue rounded-full text-sm"
                        >
                          {keyword}
                          <button
                            onClick={() => handleRemoveKeyword(index)}
                            className="ml-2 text-neon-blue/70 hover:text-red-400"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>

                  <button
                    onClick={() => handleSave('seo', seoData)}
                    disabled={saving}
                    className="btn-primary disabled:opacity-50"
                  >
                    {saving ? <LoadingSpinner size="sm" className="mr-2" /> : <Save className="w-4 h-4 mr-2" />}
                    Save SEO Settings
                  </button>
                </div>
              )}

              {/* Section Visibility Tab */}
              {activeTab === 'visibility' && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-white">Section Visibility</h3>
                  <p className="text-gray-400">Control which sections are visible on your portfolio</p>
                  
                  <div className="space-y-4">
                    {Object.entries(visibilityData).map(([section, isVisible]) => (
                      <div key={section} className="flex items-center justify-between p-4 glass rounded-lg">
                        <div>
                          <h4 className="text-white font-medium capitalize">
                            {section.replace(/([A-Z])/g, ' $1').trim()} Section
                          </h4>
                          <p className="text-sm text-gray-400">
                            {isVisible ? 'Currently visible to visitors' : 'Hidden from visitors'}
                          </p>
                        </div>
                        
                        <label className="flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={isVisible}
                            onChange={(e) => setVisibilityData(prev => ({
                              ...prev,
                              [section]: e.target.checked
                            }))}
                            className="sr-only"
                          />
                          <div className={`relative w-12 h-6 rounded-full transition-colors ${
                            isVisible ? 'bg-neon-blue' : 'bg-gray-600'
                          }`}>
                            <div className={`absolute top-1 left-1 w-4 h-4 bg-white rounded-full transition-transform ${
                              isVisible ? 'translate-x-6' : 'translate-x-0'
                            }`} />
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>

                  <button
                    onClick={() => handleSave('visibility', visibilityData)}
                    disabled={saving}
                    className="btn-primary disabled:opacity-50"
                  >
                    {saving ? <LoadingSpinner size="sm" className="mr-2" /> : <Save className="w-4 h-4 mr-2" />}
                    Save Visibility Settings
                  </button>
                </div>
              )}

              {/* Profile Tab */}
              {activeTab === 'profile' && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-white">Profile Settings</h3>
                  
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Name
                      </label>
                      <input
                        type="text"
                        value={profileData.name}
                        onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                        className="input"
                        placeholder="Your full name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Email
                      </label>
                      <input
                        type="email"
                        value={profileData.email}
                        onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                        className="input"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <button
                    onClick={handleProfileUpdate}
                    disabled={saving}
                    className="btn-primary disabled:opacity-50"
                  >
                    {saving ? <LoadingSpinner size="sm" className="mr-2" /> : <Save className="w-4 h-4 mr-2" />}
                    Update Profile
                  </button>
                </div>
              )}

              {/* Security Tab */}
              {activeTab === 'security' && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-white">Change Password</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Current Password
                      </label>
                      <input
                        type="password"
                        value={passwordData.currentPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                        className="input"
                        placeholder="Enter current password"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        New Password
                      </label>
                      <input
                        type="password"
                        value={passwordData.newPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                        className="input"
                        placeholder="Enter new password"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Confirm New Password
                      </label>
                      <input
                        type="password"
                        value={passwordData.confirmPassword}
                        onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        className="input"
                        placeholder="Confirm new password"
                      />
                    </div>
                  </div>

                  <button
                    onClick={handlePasswordChange}
                    disabled={saving || !passwordData.currentPassword || !passwordData.newPassword || passwordData.newPassword !== passwordData.confirmPassword}
                    className="btn-primary disabled:opacity-50"
                  >
                    {saving ? <LoadingSpinner size="sm" className="mr-2" /> : <Lock className="w-4 h-4 mr-2" />}
                    Change Password
                  </button>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </motion.div>
    </AdminLayout>
  );
};

export default Settings;
