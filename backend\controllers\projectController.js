const { Project } = require('../models');

// @desc    Get all projects (public)
// @route   GET /api/projects
// @access  Public
const getProjects = async (req, res) => {
  try {
    const {
      category,
      status,
      featured,
      limit = 20,
      page = 1,
      sort = '-priority,-createdAt'
    } = req.query;

    // Build query
    const query = {
      isVisible: true,
      showInPortfolio: true
    };

    if (category) query.category = category;
    if (status) query.status = status;
    if (featured === 'true') query.isFeatured = true;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute query
    const projects = await Project.find(query)
      .sort(sort)
      .limit(parseInt(limit))
      .skip(skip)
      .select('-githubData.id -views -clicks');

    // Get total count for pagination
    const total = await Project.countDocuments(query);

    res.json({
      success: true,
      data: {
        projects,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total,
          limit: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching projects'
    });
  }
};

// @desc    Get featured projects
// @route   GET /api/projects/featured
// @access  Public
const getFeaturedProjects = async (req, res) => {
  try {
    const { limit = 6 } = req.query;
    
    const projects = await Project.getFeatured(parseInt(limit));
    
    res.json({
      success: true,
      data: projects
    });
  } catch (error) {
    console.error('Get featured projects error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching featured projects'
    });
  }
};

// @desc    Get single project
// @route   GET /api/projects/:id
// @access  Public
const getProject = async (req, res) => {
  try {
    const project = await Project.findOne({
      _id: req.params.id,
      isVisible: true,
      showInPortfolio: true
    });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Increment views (optional auth to avoid counting admin views)
    if (!req.user) {
      await project.incrementViews();
    }

    res.json({
      success: true,
      data: project
    });
  } catch (error) {
    console.error('Get project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching project'
    });
  }
};

// @desc    Get all projects (admin)
// @route   GET /api/projects/admin
// @access  Private/Admin
const getProjectsAdmin = async (req, res) => {
  try {
    const {
      category,
      status,
      featured,
      visible,
      limit = 50,
      page = 1,
      sort = '-createdAt',
      search
    } = req.query;

    // Build query
    const query = {};

    if (category) query.category = category;
    if (status) query.status = status;
    if (featured !== undefined) query.isFeatured = featured === 'true';
    if (visible !== undefined) query.isVisible = visible === 'true';

    // Add search functionality
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { title: searchRegex },
        { description: searchRegex },
        { shortDescription: searchRegex },
        { 'technologies.name': searchRegex },
        { tags: searchRegex }
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute query
    const projects = await Project.find(query)
      .sort(sort)
      .limit(parseInt(limit))
      .skip(skip);

    // Get total count for pagination
    const total = await Project.countDocuments(query);

    res.json({
      success: true,
      data: {
        projects,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total,
          limit: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get projects admin error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching projects'
    });
  }
};

// @desc    Create new project
// @route   POST /api/projects
// @access  Private/Admin
const createProject = async (req, res) => {
  try {
    const project = await Project.create(req.body);
    
    res.status(201).json({
      success: true,
      message: 'Project created successfully',
      data: project
    });
  } catch (error) {
    console.error('Create project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error creating project'
    });
  }
};

// @desc    Update project
// @route   PUT /api/projects/:id
// @access  Private/Admin
const updateProject = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Update project fields
    Object.keys(req.body).forEach(key => {
      if (req.body[key] !== undefined) {
        project[key] = req.body[key];
      }
    });

    await project.save();
    
    res.json({
      success: true,
      message: 'Project updated successfully',
      data: project
    });
  } catch (error) {
    console.error('Update project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error updating project'
    });
  }
};

// @desc    Delete project
// @route   DELETE /api/projects/:id
// @access  Private/Admin
const deleteProject = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    await project.deleteOne();
    
    res.json({
      success: true,
      message: 'Project deleted successfully'
    });
  } catch (error) {
    console.error('Delete project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error deleting project'
    });
  }
};

// @desc    Toggle project visibility
// @route   PATCH /api/projects/:id/visibility
// @access  Private/Admin
const toggleVisibility = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    project.isVisible = !project.isVisible;
    await project.save();
    
    res.json({
      success: true,
      message: `Project ${project.isVisible ? 'shown' : 'hidden'} successfully`,
      data: { isVisible: project.isVisible }
    });
  } catch (error) {
    console.error('Toggle visibility error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error toggling project visibility'
    });
  }
};

// @desc    Toggle featured status
// @route   PATCH /api/projects/:id/featured
// @access  Private/Admin
const toggleFeatured = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    project.isFeatured = !project.isFeatured;
    await project.save();
    
    res.json({
      success: true,
      message: `Project ${project.isFeatured ? 'featured' : 'unfeatured'} successfully`,
      data: { isFeatured: project.isFeatured }
    });
  } catch (error) {
    console.error('Toggle featured error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error toggling featured status'
    });
  }
};

// @desc    Track project click
// @route   POST /api/projects/:id/click
// @access  Public
const trackClick = async (req, res) => {
  try {
    const project = await Project.findById(req.params.id);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    await project.incrementClicks();
    
    res.json({
      success: true,
      message: 'Click tracked successfully'
    });
  } catch (error) {
    console.error('Track click error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error tracking click'
    });
  }
};

module.exports = {
  getProjects,
  getFeaturedProjects,
  getProject,
  getProjectsAdmin,
  createProject,
  updateProject,
  deleteProject,
  toggleVisibility,
  toggleFeatured,
  trackClick
};
